import { NextRequest, NextResponse } from 'next/server'
import { autoAssignTask, createTaskRouter, type EligibleAgent } from '@/lib/task-router'
import { createEnhancedTaskRouter } from '@/lib/enhanced-task-router'
import { getDemoOrgId } from '@/lib/db-utils'
import { prisma } from '@/lib/prisma'
import { UserStatus } from '@prisma/client'
import type { EnhancedRoutingStrategy, RoutingContext, AgentWithSkills, TaskWithSkills } from '@/lib/types'

// POST /api/tasks/[id]/assign - Manually trigger task assignment
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const strategy = body.strategy || 'round_robin'

    // Validate strategy - support both legacy and enhanced strategies
    const legacyStrategies = ['round_robin', 'least_loaded', 'weighted_round_robin', 'skill_based']
    const enhancedStrategies: EnhancedRoutingStrategy[] = ['weighted_round_robin', 'best_skill_match', 'performance_based', 'hybrid_intelligent']
    const allValidStrategies = [...legacyStrategies, ...enhancedStrategies]

    if (!allValidStrategies.includes(strategy)) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'INVALID_STRATEGY',
            message: `Invalid routing strategy. Must be one of: ${allValidStrategies.join(', ')}`,
          },
        },
        { status: 400 }
      )
    }

    // Check if task exists
    const task = await prisma.task.findUnique({
      where: { id: params.id },
      include: {
        assignedUser: {
          select: { name: true, email: true }
        }
      }
    })

    if (!task) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'TASK_NOT_FOUND',
            message: 'Task not found',
          },
        },
        { status: 404 }
      )
    }

    if (task.assignedTo) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'TASK_ALREADY_ASSIGNED',
            message: `Task is already assigned to ${task.assignedUser?.name}`,
          },
        },
        { status: 409 }
      )
    }

    // Get organization ID
    const organizationId = await getDemoOrgId()

    // Use enhanced routing for enhanced strategies
    if (enhancedStrategies.includes(strategy as EnhancedRoutingStrategy)) {
      const routingResult = await performEnhancedRouting(params.id, organizationId, strategy as EnhancedRoutingStrategy)

      if (!routingResult.success) {
        return NextResponse.json(
          {
            success: false,
            error: {
              code: 'ASSIGNMENT_FAILED',
              message: routingResult.error || 'Enhanced assignment failed',
            },
          },
          { status: 400 }
        )
      }

      return NextResponse.json({
        success: true,
        data: {
          task: routingResult.task,
          assignedAgent: routingResult.assignedAgent,
          confidence: routingResult.confidence,
          reasoning: routingResult.reasoning,
          alternativeAgents: routingResult.alternativeAgents
        },
        message: `Task assigned successfully using ${strategy} strategy`,
      })
    }

    // Fallback to legacy routing
    const routingResult = await autoAssignTask(params.id, organizationId, strategy)

    if (!routingResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'ASSIGNMENT_FAILED',
            message: routingResult.error || routingResult.reason || 'Assignment failed',
          },
        },
        { status: 400 }
      )
    }

    // Get updated task
    const updatedTask = await prisma.task.findUnique({
      where: { id: params.id },
      include: {
        assignedUser: {
          select: {
            id: true,
            name: true,
            email: true,
            status: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    })

    return NextResponse.json({
      success: true,
      data: updatedTask,
      message: `Task assigned to ${routingResult.assignedAgent?.name} using ${strategy} strategy`,
      routing: {
        strategy,
        assignedAgent: routingResult.assignedAgent?.name,
        reason: routingResult.reason,
      },
    })

  } catch (error) {
    console.error('Error in manual task assignment:', error)
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'ASSIGNMENT_ERROR',
          message: 'Failed to assign task',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      },
      { status: 500 }
    )
  }
}

// GET /api/tasks/[id]/assign - Get assignment recommendations
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if task exists
    const task = await prisma.task.findUnique({
      where: { id: params.id },
      include: {
        assignedUser: {
          select: { name: true, email: true }
        }
      }
    })

    if (!task) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'TASK_NOT_FOUND',
            message: 'Task not found',
          },
        },
        { status: 404 }
      )
    }

    if (task.assignedTo) {
      return NextResponse.json({
        success: true,
        data: {
          taskId: params.id,
          currentAssignment: {
            agentId: task.assignedTo,
            agentName: task.assignedUser?.name,
            agentEmail: task.assignedUser?.email,
          },
          canReassign: true,
          recommendations: [],
        },
        message: 'Task is already assigned',
      })
    }

    // Get organization ID and create router
    const organizationId = await getDemoOrgId()
    const router = createTaskRouter(organizationId)

    // Get eligible agents (this gives us the recommendation data)
    const eligibleAgents: EligibleAgent[] = await router.getEligibleAgents()

    const recommendations = eligibleAgents.map((agent: EligibleAgent) => ({
      agentId: agent.id,
      agentName: agent.name,
      agentEmail: agent.email,
      currentTasks: agent.currentTaskCount,
      maxTasks: agent.maxConcurrentTasks,
      utilizationRate: agent.utilizationRate,
      lastAssignedAt: agent.lastAssignedAt,
      status: agent.status,
    }))

    // Sort recommendations by suitability (lowest utilization first)
    recommendations.sort((a, b) => a.utilizationRate - b.utilizationRate)

    return NextResponse.json({
      success: true,
      data: {
        taskId: params.id,
        currentAssignment: null,
        canAssign: recommendations.length > 0,
        recommendations,
        routingStats: await router.getRoutingStats(),
      },
      message: recommendations.length > 0 
        ? `Found ${recommendations.length} eligible agents` 
        : 'No eligible agents available',
    })

  } catch (error) {
    console.error('Error getting assignment recommendations:', error)
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'RECOMMENDATION_ERROR',
          message: 'Failed to get assignment recommendations',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      },
      { status: 500 }
    )
  }
}

// Enhanced routing function
async function performEnhancedRouting(taskId: string, organizationId: string, strategy: EnhancedRoutingStrategy) {
  try {
    // Get task with skills
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        requiredSkills: true,
        organization: true
      }
    }) as TaskWithSkills | null

    if (!task) {
      return { success: false, error: 'Task not found' }
    }

    // Get all agents with skills and performance
    const allAgents = await prisma.user.findMany({
      where: {
        organizationId,
        status: UserStatus.AVAILABLE,
        currentTaskCount: {
          lt: prisma.user.fields.maxConcurrentTasks
        }
      },
      include: {
        skills: true,
        performance: true
      }
    }) as AgentWithSkills[]

    if (allAgents.length === 0) {
      return { success: false, error: 'No agents in organization' }
    }

    // Filter agents by working hours and availability
    const { createWorkingHoursManager } = await import('@/lib/working-hours-manager')
    const workingHoursManager = createWorkingHoursManager()

    const availableAgents: AgentWithSkills[] = []

    for (const agent of allAgents) {
      // Check if agent is available considering working hours, availability status, and capacity
      const isAvailable = await workingHoursManager.isAgentAvailable(agent.id)

      if (isAvailable) {
        availableAgents.push(agent)
      }
    }

    // Handle urgent tasks override
    if (availableAgents.length === 0 && task.priority === 'URGENT') {
      const orgSettings = await workingHoursManager.getOrganizationSettings(organizationId)

      if (orgSettings.urgentTasksOverride) {
        // For urgent tasks, include agents even if outside working hours
        // but still respect availability status and capacity
        for (const agent of allAgents) {
          const availability = await workingHoursManager.getAgentAvailability(agent.id)

          if (!availability || availability.status === 'available') {
            availableAgents.push(agent)
          }
        }
      }
    }

    if (availableAgents.length === 0) {
      return { success: false, error: 'No available agents considering working hours and availability' }
    }

    // Create routing context
    const context: RoutingContext = {
      task,
      availableAgents,
      organizationId,
      strategy
    }

    // Use enhanced router
    const enhancedRouter = createEnhancedTaskRouter(organizationId)
    const routingResult = await enhancedRouter.routeTask(context)

    // Assign the task
    const updatedTask = await prisma.task.update({
      where: { id: taskId },
      data: {
        assignedTo: routingResult.selectedAgent.id,
        assignedAt: new Date(),
        status: 'ASSIGNED'
      },
      include: {
        assignedUser: {
          select: { id: true, name: true, email: true }
        }
      }
    })

    // Update agent task count
    await prisma.user.update({
      where: { id: routingResult.selectedAgent.id },
      data: {
        currentTaskCount: {
          increment: 1
        }
      }
    })

    return {
      success: true,
      task: updatedTask,
      assignedAgent: routingResult.selectedAgent,
      confidence: routingResult.confidence,
      reasoning: routingResult.reasoning,
      alternativeAgents: routingResult.alternativeAgents
    }

  } catch (error) {
    console.error('Enhanced routing error:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Enhanced routing failed'
    }
  }
}
