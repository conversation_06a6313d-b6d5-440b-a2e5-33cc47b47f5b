# Task Routing & Load Balancing Platform

A modern SaaS platform that intelligently distributes tasks (support tickets, leads, work requests) across team members based on availability, skills, workload, and performance metrics. Built with Next.js 14, TypeScript, and PostgreSQL.

## 🎯 Overview

This platform eliminates manual task assignment overhead by automatically routing tasks to the most suitable team members using advanced algorithms. It ensures optimal resource utilization while maintaining quality and response times, helping teams scale operations without proportional management overhead.

**🚀 Live Demo**: [View the application](http://localhost:3000) (when running locally)

### Key Benefits

- **🎯 Intelligent Task Distribution** - Advanced algorithms assign tasks based on agent availability, skills, and workload
- **⚖️ Real-time Workload Balancing** - Prevents burnout by distributing tasks evenly across team members
- **📊 Performance Tracking** - Monitor response times, completion rates, and team efficiency
- **🏢 Multi-tenant Architecture** - Supports multiple organizations with complete data isolation
- **🔐 Role-based Access Control** - Separate dashboards for admins and agents
- **📱 Modern UI/UX** - Beautiful, responsive interface built with shadcn/ui and Tailwind CSS

## 🚀 Features

### ✅ Current Features (Implemented)
- **🔐 User Authentication** - NextAuth.js with credential-based login and role-based access
- **📋 Task Management** - Create, assign, update, and track tasks with priority levels
- **👥 Agent Management** - Team member profiles with capacity and availability tracking
- **🧠 Intelligent Routing** - Multiple assignment strategies:
  - Weighted Round Robin with skill matching
  - Best Skill Match prioritization
  - Performance-based routing
  - Hybrid intelligent strategy with dynamic weighting
- **📊 Real-time Dashboards** - Separate admin and agent interfaces with live updates
- **🔧 Complete API** - RESTful endpoints for all operations
- **🗄️ Robust Database** - Optimized PostgreSQL schema with comprehensive audit trails
- **🎨 Modern Landing Page** - Professional marketing site with gradient design
- **📱 Responsive Design** - Works seamlessly on desktop, tablet, and mobile

### 🔄 Enhanced Features (Recently Added)
- **🎯 Skill-based Routing** - Advanced skill matching with proficiency levels
- **📈 Performance Analytics** - Agent performance tracking and metrics
- **⚡ Enhanced Routing Engine** - Multi-factor scoring algorithms
- **🎨 Beautiful UI** - Modern design with hover effects and animations
- **📊 Kanban Board** - Drag-and-drop task management for agents

### 🚀 Future Enhancements
- **⏰ SLA Management** - Deadline tracking and escalation workflows
- **🕐 Working Hours** - Timezone-aware availability management
- **🔔 Real-time Notifications** - WebSocket-based live updates
- **📧 Multi-channel Integration** - Email, API, and form-based task ingestion
- **🤖 Machine Learning** - AI-powered routing optimization (future consideration)

## 🛠️ Technology Stack

- **Frontend**: Next.js 14 with TypeScript
- **Backend**: Next.js API Routes
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js
- **UI Components**: shadcn/ui with Tailwind CSS
- **Forms**: React Hook Form with Zod validation
- **Charts**: Recharts for analytics
- **Deployment**: Vercel-ready

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd task-saas
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```

   Configure the following variables:
   ```env
   DATABASE_URL="postgresql://username:password@localhost:5432/taskrouting"
   NEXTAUTH_SECRET="your-secret-key"
   NEXTAUTH_URL="http://localhost:3000"
   ```

4. **Set up the database**
   ```bash
   # Generate Prisma client
   npm run db:generate

   # Push schema to database
   npm run db:push

   # Seed with demo data
   npm run db:seed
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🎮 Demo Accounts

The application comes with pre-seeded demo accounts for testing:

### 👨‍💼 Admin Account
- **Email**: <EMAIL>
- **Password**: demo123
- **Access**: Full system administration, team management, analytics

### 👩‍💻 Agent Accounts
- **Alice Johnson**: <EMAIL> / demo123 (Available)
- **Bob Smith**: <EMAIL> / demo123 (Busy)
- **Charlie Brown**: <EMAIL> / demo123 (Available)

> **Note**: All demo accounts use the password `demo123` for consistency

## 📊 API Documentation

### Task Management
```
GET    /api/tasks              # List tasks with filtering
POST   /api/tasks              # Create new task
GET    /api/tasks/[id]          # Get task details
PATCH  /api/tasks/[id]          # Update task
DELETE /api/tasks/[id]          # Delete task
POST   /api/tasks/[id]/assign   # Manual task assignment
```

### Agent Management
```
GET    /api/agents             # List agents
POST   /api/agents             # Create agent
GET    /api/agents/[id]         # Get agent details
PATCH  /api/agents/[id]         # Update agent
DELETE /api/agents/[id]         # Delete agent
PATCH  /api/agents/[id]/status  # Update availability
```

### Analytics
```
GET    /api/routing/stats       # Routing statistics and metrics
GET    /api/test-db             # Database connection test
```

## 🗄️ Database Schema

### Core Models
- **Organization** - Multi-tenant isolation
- **User** - Agents with capacity and status tracking
- **Task** - Core task entity with assignment tracking
- **TaskEvent** - Complete audit trail

### Key Features
- Multi-tenant architecture with organization-based isolation
- User capacity management (current vs max concurrent tasks)
- Task priority and status tracking with SLA support
- Comprehensive audit trail via task events
- Optimized indexes for performance

## 🎯 Intelligent Routing Algorithms

### 🔄 Weighted Round Robin
Advanced algorithm combining multiple factors:
- **Workload Score** (40%) - Current task load vs capacity
- **Skill Match Score** (40%) - Proficiency level matching
- **Performance Score** (20%) - Historical completion rates

### 🎯 Best Skill Match
Prioritizes skill compatibility:
- **Skill Matching** (80%) - Required vs available skills
- **Workload Balance** (20%) - Secondary consideration

### 📈 Performance-Based Routing
Focuses on historical performance:
- **Performance Metrics** (50%) - Success rates and quality
- **Skill Matching** (30%) - Competency alignment
- **Workload Balance** (20%) - Capacity management

### 🧠 Hybrid Intelligent Strategy
Dynamic weighting based on task characteristics:
- **Urgency-based Adjustments** - High priority tasks get performance focus
- **Skill-critical Tasks** - Complex tasks prioritize expertise
- **Load Balancing** - Prevents agent burnout during peak times

## 📱 User Interface

### 👨‍💼 Admin Dashboard
- **📊 System Overview** - Real-time metrics and KPIs
- **👥 Team Management** - Agent profiles, skills, and availability
- **📋 Task Management** - Create, assign, and monitor all tasks
- **📈 Performance Analytics** - Team efficiency and routing statistics
- **⚙️ System Configuration** - Routing strategies and settings

### 👩‍💻 Agent Dashboard
- **📝 Personal Task Queue** - Assigned tasks with priorities
- **🎯 Kanban Board** - Drag-and-drop task management
- **📊 Individual Metrics** - Personal performance tracking
- **🔄 Status Management** - Availability and capacity updates
- **✅ Task Actions** - Update, complete, and escalate tasks

### 🎨 Modern Design Features
- **Responsive Layout** - Works on all device sizes
- **Dark/Light Themes** - Professional gradient designs
- **Interactive Elements** - Hover effects and smooth animations
- **Real-time Updates** - Live data without page refreshes

## 🔧 Development Scripts

```bash
# Development
npm run dev              # Start development server
npm run build            # Build for production
npm run start            # Start production server
npm run lint             # Run ESLint

# Database
npm run db:generate      # Generate Prisma client
npm run db:push          # Push schema to database
npm run db:migrate       # Run database migrations
npm run db:studio        # Open Prisma Studio
npm run db:seed          # Seed demo data
```

## 🏗️ Project Structure

```
├── app/                     # Next.js App Router
│   ├── admin/              # Admin dashboard
│   ├── agent/              # Agent dashboard
│   ├── auth/               # Authentication pages
│   ├── dashboard/          # Main dashboard
│   ├── api/                # API routes
│   │   ├── agents/         # Agent management endpoints
│   │   ├── auth/           # Authentication endpoints
│   │   ├── routing/        # Routing statistics
│   │   └── tasks/          # Task management endpoints
│   ├── layout.tsx          # Root layout
│   └── page.tsx            # Homepage
├── components/             # React components
│   ├── ui/                 # shadcn/ui components
│   ├── admin-dashboard.tsx # Admin interface
│   ├── agent-dashboard.tsx # Agent interface
│   ├── task-creation-form.tsx # Task creation modal
│   └── providers.tsx       # Context providers
├── lib/                    # Utility libraries
│   ├── auth.ts             # NextAuth configuration
│   ├── db-utils.ts         # Database utilities
│   ├── enhanced-task-router.ts # Advanced routing algorithms
│   ├── prisma.ts           # Prisma client
│   ├── skills-utils.ts     # Skill matching utilities
│   ├── task-router.ts      # Core routing logic
│   ├── types.ts            # TypeScript types
│   └── utils.ts            # General utilities
├── prisma/                 # Database schema and migrations
│   ├── migrations/         # Database migrations
│   ├── schema.prisma       # Database schema
│   └── seed.ts             # Demo data seeding
├── types/                  # Type definitions
│   └── next-auth.d.ts      # NextAuth type extensions
├── docs/                   # Documentation
│   ├── phase1-implementation-summary.md
│   └── task_routing_architecture.md
└── middleware.ts           # Route protection middleware
```

## 🔐 Authentication & Security

### Authentication Flow
1. **Credential-based Login** - Email and password authentication
2. **JWT Sessions** - Secure session management with NextAuth.js
3. **Role-based Access** - Admin and Agent role separation
4. **Route Protection** - Middleware-based route guards
5. **Organization Isolation** - Multi-tenant data separation

### Security Features
- Password hashing with bcrypt
- CSRF protection via NextAuth.js
- SQL injection prevention with Prisma
- Input validation with Zod schemas
- Environment variable protection

## 📈 Performance & Monitoring

### Database Optimization
- Optimized indexes for common queries
- Efficient pagination for large datasets
- Connection pooling with Prisma
- Query optimization for dashboard statistics

### Monitoring Endpoints
- `/api/test-db` - Database connectivity test
- `/api/routing/stats` - System performance metrics
- Health checks for all core services

## 🧪 Testing

### 🎭 Demo Data
The application includes comprehensive demo data:
- **1 Demo Organization** - "Demo Organization" with full setup
- **4 Users** - 1 Admin + 3 Agents with different skills and availability
- **Sample Tasks** - Various priorities, types, and assignment states
- **Skill Profiles** - Agents with different expertise levels
- **Performance History** - Simulated task completion data

### 🧪 Testing Scenarios
1. **🎯 Intelligent Routing** - Create tasks and watch automatic assignment
2. **🔄 Strategy Comparison** - Test different routing algorithms
3. **📊 Real-time Updates** - Monitor dashboard metrics and live changes
4. **👥 Role-based Access** - Compare admin vs agent interfaces
5. **🎨 UI/UX Testing** - Explore responsive design and interactions
6. **📱 Kanban Workflow** - Drag and drop tasks between status columns

## 🚀 Deployment

### Environment Variables
```env
# Database
DATABASE_URL="postgresql://..."

# Authentication
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="https://your-domain.com"

# Optional: Additional configuration
NODE_ENV="production"
```

### Deployment Platforms
- **Vercel** (Recommended) - Zero-config deployment
- **Railway** - Database and app hosting
- **Heroku** - Traditional PaaS deployment
- **Docker** - Containerized deployment

### Database Hosting
- **Neon** (Recommended) - Serverless PostgreSQL
- **Supabase** - Open-source Firebase alternative
- **PlanetScale** - MySQL-compatible serverless database
- **AWS RDS** - Managed relational database

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch** (`git checkout -b feature/amazing-feature`)
3. **Commit your changes** (`git commit -m 'Add amazing feature'`)
4. **Push to the branch** (`git push origin feature/amazing-feature`)
5. **Open a Pull Request**

### Development Guidelines
- Follow TypeScript best practices
- Use Prettier for code formatting
- Write meaningful commit messages
- Add tests for new features
- Update documentation as needed

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Next.js** - React framework for production
- **Prisma** - Next-generation ORM for Node.js
- **shadcn/ui** - Beautiful and accessible UI components
- **NextAuth.js** - Complete authentication solution
- **Tailwind CSS** - Utility-first CSS framework

## 📞 Support & Community

For support and questions:
- 📖 **Documentation**: [docs/](./docs/) - Comprehensive guides and API docs
- 🐛 **Issues**: [GitHub Issues](https://github.com/your-username/task-saas/issues) - Bug reports and feature requests
- 💬 **Discussions**: [GitHub Discussions](https://github.com/your-username/task-saas/discussions) - Community Q&A
- ⭐ **Star the repo** if you find it useful!

## 🌟 Screenshots

| Admin Dashboard | Agent Kanban Board |
|---|---|
| ![Admin Dashboard](docs/screenshots/admin-dashboard.png) | ![Agent Board](docs/screenshots/agent-kanban.png) |

---

**Built with ❤️ for efficient team management and intelligent task distribution.**

> **⭐ If this project helps you, please consider giving it a star on GitHub!**
