import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { createWorkingHoursManager } from "@/lib/working-hours-manager";
import { OrganizationWorkingSettings } from "@/lib/types";

/**
 * GET /api/organizations/[id]/settings
 * Get organization working settings
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const organizationId = params.id;

    // Check if user belongs to this organization
    if (session.user.organizationId !== organizationId) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const workingHoursManager = createWorkingHoursManager();
    const settings = await workingHoursManager.getOrganizationSettings(organizationId);

    return NextResponse.json({
      success: true,
      data: settings,
    });
  } catch (error) {
    console.error("Error fetching organization settings:", error);
    return NextResponse.json(
      { error: "Failed to fetch organization settings" },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/organizations/[id]/settings
 * Update organization settings
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const organizationId = params.id;

    // Check if user is admin and belongs to this organization
    if (
      session.user.role !== "ADMIN" ||
      session.user.organizationId !== organizationId
    ) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body: Partial<OrganizationWorkingSettings> = await request.json();

    // Validate request body
    if (body.businessHoursStart && !body.businessHoursStart.match(/^\d{2}:\d{2}$/)) {
      return NextResponse.json(
        { error: "Invalid businessHoursStart format. Use HH:MM" },
        { status: 400 }
      );
    }

    if (body.businessHoursEnd && !body.businessHoursEnd.match(/^\d{2}:\d{2}$/)) {
      return NextResponse.json(
        { error: "Invalid businessHoursEnd format. Use HH:MM" },
        { status: 400 }
      );
    }

    if (body.businessDays) {
      if (
        !Array.isArray(body.businessDays) ||
        !body.businessDays.every(
          (day) => typeof day === "number" && day >= 0 && day <= 6
        )
      ) {
        return NextResponse.json(
          { error: "Invalid businessDays format. Must be array of numbers 0-6" },
          { status: 400 }
        );
      }
    }

    if (
      body.afterHoursRouting !== undefined &&
      typeof body.afterHoursRouting !== "boolean"
    ) {
      return NextResponse.json(
        { error: "afterHoursRouting must be a boolean" },
        { status: 400 }
      );
    }

    if (
      body.weekendRouting !== undefined &&
      typeof body.weekendRouting !== "boolean"
    ) {
      return NextResponse.json(
        { error: "weekendRouting must be a boolean" },
        { status: 400 }
      );
    }

    if (
      body.urgentTasksOverride !== undefined &&
      typeof body.urgentTasksOverride !== "boolean"
    ) {
      return NextResponse.json(
        { error: "urgentTasksOverride must be a boolean" },
        { status: 400 }
      );
    }

    if (
      body.overrideAgentHours !== undefined &&
      typeof body.overrideAgentHours !== "boolean"
    ) {
      return NextResponse.json(
        { error: "overrideAgentHours must be a boolean" },
        { status: 400 }
      );
    }

    const workingHoursManager = createWorkingHoursManager();
    const settings = await workingHoursManager.updateOrganizationSettings(
      organizationId,
      body
    );

    return NextResponse.json({
      success: true,
      data: settings,
      message: "Organization settings updated successfully",
    });
  } catch (error) {
    console.error("Error updating organization settings:", error);
    return NextResponse.json(
      { error: "Failed to update organization settings" },
      { status: 500 }
    );
  }
}
