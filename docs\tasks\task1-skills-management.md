# Task 1: Skills Management System

## 🎯 Objective

Implement a comprehensive skills management system that allows agents to have multiple skills and tasks to require specific skills for intelligent routing.

## 📋 Status

- **Priority**: P1 (Critical)
- **Status**: 🔄 Ready to Start
- **Estimated Time**: 4-6 hours
- **Dependencies**: None (Foundation task)

## 🔧 Technical Requirements

### Database Schema Updates

1. **Agent Skills Table**
   ```sql
   -- New table for agent skills
   CREATE TABLE agent_skills (
     id UUID PRIMARY KEY,
     agent_id UUID REFERENCES users(id),
     skill_name VARCHAR(100) NOT NULL,
     proficiency_level INTEGER DEFAULT 1, -- 1-5 scale
     created_at TIMESTAMP DEFAULT NOW(),
     UNIQUE(agent_id, skill_name)
   );
   ```

2. **Task Required Skills Table**
   ```sql
   -- New table for task skill requirements
   CREATE TABLE task_required_skills (
     id UUID PRIMARY KEY,
     task_id UUID REFERENCES tasks(id),
     skill_name VARCHAR(100) NOT NULL,
     required_level INTEGER DEFAULT 1, -- 1-5 scale
     created_at TIMESTAMP DEFAULT NOW(),
     UNIQUE(task_id, skill_name)
   );
   ```

3. **Skills Catalog Table**
   ```sql
   -- Master skills catalog for organization
   CREATE TABLE skills_catalog (
     id UUID PRIMARY KEY,
     organization_id UUID REFERENCES organizations(id),
     name VARCHAR(100) NOT NULL,
     description TEXT,
     category VARCHAR(50), -- 'technical', 'language', 'domain', etc.
     is_active BOOLEAN DEFAULT true,
     created_at TIMESTAMP DEFAULT NOW(),
     UNIQUE(organization_id, name)
   );
   ```

### Prisma Schema Updates

```prisma
model AgentSkill {
  id               String   @id @default(cuid())
  agentId          String   @map("agent_id")
  skillName        String   @map("skill_name")
  proficiencyLevel Int      @default(1) @map("proficiency_level")
  createdAt        DateTime @default(now()) @map("created_at")
  
  agent User @relation(fields: [agentId], references: [id], onDelete: Cascade)
  
  @@unique([agentId, skillName])
  @@map("agent_skills")
}

model TaskRequiredSkill {
  id            String   @id @default(cuid())
  taskId        String   @map("task_id")
  skillName     String   @map("skill_name")
  requiredLevel Int      @default(1) @map("required_level")
  createdAt     DateTime @default(now()) @map("created_at")
  
  task Task @relation(fields: [taskId], references: [id], onDelete: Cascade)
  
  @@unique([taskId, skillName])
  @@map("task_required_skills")
}

model SkillsCatalog {
  id             String   @id @default(cuid())
  organizationId String   @map("organization_id")
  name           String
  description    String?
  category       String?
  isActive       Boolean  @default(true) @map("is_active")
  createdAt      DateTime @default(now()) @map("created_at")
  
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  @@unique([organizationId, name])
  @@map("skills_catalog")
}
```

## 🛠️ Implementation Steps

### Step 1: Database Schema Migration (30 minutes)

1. **Update Prisma Schema**
   - Add new models to `prisma/schema.prisma`
   - Add relations to existing User and Task models

2. **Create Migration**
   ```bash
   npx prisma migrate dev --name add-skills-management
   ```

3. **Update Seed Data**
   - Add sample skills to seed file
   - Assign skills to existing demo agents

### Step 2: TypeScript Types (20 minutes)

1. **Update `lib/types.ts`**
   ```typescript
   export interface AgentSkill {
     id: string;
     agentId: string;
     skillName: string;
     proficiencyLevel: number;
     createdAt: Date;
   }

   export interface TaskRequiredSkill {
     id: string;
     taskId: string;
     skillName: string;
     requiredLevel: number;
     createdAt: Date;
   }

   export interface SkillsCatalogItem {
     id: string;
     organizationId: string;
     name: string;
     description?: string;
     category?: string;
     isActive: boolean;
     createdAt: Date;
   }

   export interface AgentWithSkills extends User {
     skills: AgentSkill[];
   }

   export interface TaskWithSkills extends Task {
     requiredSkills: TaskRequiredSkill[];
   }
   ```

### Step 3: Database Utilities (45 minutes)

1. **Create `lib/skills-utils.ts`**
   ```typescript
   import { prisma } from './prisma';
   
   export class SkillsManager {
     // Agent skills management
     async addSkillToAgent(agentId: string, skillName: string, proficiencyLevel: number = 1) {
       // Implementation
     }
     
     async removeSkillFromAgent(agentId: string, skillName: string) {
       // Implementation
     }
     
     async updateAgentSkillLevel(agentId: string, skillName: string, proficiencyLevel: number) {
       // Implementation
     }
     
     async getAgentSkills(agentId: string) {
       // Implementation
     }
     
     // Task skills management
     async addSkillRequirementToTask(taskId: string, skillName: string, requiredLevel: number = 1) {
       // Implementation
     }
     
     async removeSkillRequirementFromTask(taskId: string, skillName: string) {
       // Implementation
     }
     
     async getTaskSkillRequirements(taskId: string) {
       // Implementation
     }
     
     // Skills catalog management
     async createSkill(organizationId: string, name: string, description?: string, category?: string) {
       // Implementation
     }
     
     async getOrganizationSkills(organizationId: string) {
       // Implementation
     }
     
     // Skill matching utilities
     async findAgentsWithSkills(organizationId: string, requiredSkills: string[]) {
       // Implementation
     }
     
     async calculateSkillMatch(agentSkills: AgentSkill[], requiredSkills: TaskRequiredSkill[]) {
       // Implementation - returns match percentage
     }
   }
   ```

### Step 4: API Endpoints (90 minutes)

1. **Skills Catalog API** - `app/api/skills/route.ts`
   - GET: List organization skills
   - POST: Create new skill

2. **Agent Skills API** - `app/api/agents/[id]/skills/route.ts`
   - GET: Get agent skills
   - POST: Add skill to agent
   - DELETE: Remove skill from agent

3. **Task Skills API** - `app/api/tasks/[id]/skills/route.ts`
   - GET: Get task skill requirements
   - POST: Add skill requirement to task
   - DELETE: Remove skill requirement from task

### Step 5: Enhanced Database Utilities (60 minutes)

1. **Update `lib/db-utils.ts`**
   - Modify agent queries to include skills
   - Modify task queries to include skill requirements
   - Add skill-based filtering functions

2. **Update Task Router**
   - Modify `getEligibleAgents` to consider skills
   - Add skill matching logic
   - Update routing strategies to factor in skill match quality

## 🧪 Testing Requirements

### Unit Tests
- Skills CRUD operations
- Skill matching algorithms
- Agent eligibility with skills

### Integration Tests
- API endpoints functionality
- Database constraints and relationships
- Skill-based task routing

### Manual Testing
- Create skills in organization
- Assign skills to agents
- Create tasks with skill requirements
- Verify routing considers skills

## 📝 Acceptance Criteria

- [x] Agents can have multiple skills with proficiency levels
- [x] Tasks can require specific skills with minimum levels
- [ ] Organization has a skills catalog for consistency
- [x] API endpoints for managing skills are functional
- [x] Task routing considers skill requirements
- [x] Database relationships are properly enforced
- [x] Existing functionality remains unaffected

## 🔗 Dependencies for Next Tasks

This task provides the foundation for:
- **Task 2**: Enhanced routing algorithms using skill matching
- **Task 3**: Working hours (skills + availability)
- **Task 4**: Routing rules (skill-based rules)

## 📋 Deliverables

1. Updated Prisma schema with skills models
2. Database migration files
3. TypeScript types for skills
4. Skills management utility functions
5. API endpoints for skills CRUD
6. Updated seed data with sample skills
7. Enhanced task router with skill matching
8. Unit tests for skills functionality

**Next Task**: [Task 2: Enhanced Routing Engine](./task2-enhanced-routing.md)
