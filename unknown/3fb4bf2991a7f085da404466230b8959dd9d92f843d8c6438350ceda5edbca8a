"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Clock, Save, RefreshCw } from "lucide-react";
import { toast } from "sonner";

interface WorkingHours {
  id: string;
  agentId: string;
  dayOfWeek: number;
  startTime: string;
  endTime: string;
  timezone: string;
  isActive: boolean;
}

interface WorkingHoursManagerProps {
  agentId: string;
}

const DAYS_OF_WEEK = [
  { value: 0, label: "Sunday" },
  { value: 1, label: "Monday" },
  { value: 2, label: "Tuesday" },
  { value: 3, label: "Wednesday" },
  { value: 4, label: "Thursday" },
  { value: 5, label: "Friday" },
  { value: 6, label: "Saturday" },
];

const TIMEZONES = [
  { value: "UTC", label: "UTC" },
  { value: "America/New_York", label: "Eastern Time (ET)" },
  { value: "America/Chicago", label: "Central Time (CT)" },
  { value: "America/Denver", label: "Mountain Time (MT)" },
  { value: "America/Los_Angeles", label: "Pacific Time (PT)" },
  { value: "Europe/London", label: "London (GMT)" },
  { value: "Europe/Paris", label: "Paris (CET)" },
  { value: "Asia/Tokyo", label: "Tokyo (JST)" },
];

export function WorkingHoursManager({ agentId }: WorkingHoursManagerProps) {
  const [workingHours, setWorkingHours] = useState<WorkingHours[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Initialize default schedule (Monday-Friday 9-5 UTC)
  const initializeDefaultSchedule = useCallback(() => {
    const defaultSchedule = DAYS_OF_WEEK.map((day) => ({
      id: `temp-${day.value}`,
      agentId,
      dayOfWeek: day.value,
      startTime: day.value >= 1 && day.value <= 5 ? "09:00" : "09:00",
      endTime: day.value >= 1 && day.value <= 5 ? "17:00" : "17:00",
      timezone: "UTC",
      isActive: day.value >= 1 && day.value <= 5, // Monday-Friday active by default
    }));
    setWorkingHours(defaultSchedule);
  }, [agentId]);

  // Fetch working hours
  const fetchWorkingHours = useCallback(async () => {
    try {
      const response = await fetch(`/api/agents/${agentId}/working-hours`);
      const result = await response.json();

      if (result.success) {
        if (result.data.length === 0) {
          // No working hours set, initialize with defaults
          initializeDefaultSchedule();
        } else {
          // Fill in missing days with defaults
          const completeSchedule = DAYS_OF_WEEK.map((day) => {
            const existing = result.data.find((wh: WorkingHours) => wh.dayOfWeek === day.value);
            return existing || {
              id: `temp-${day.value}`,
              agentId,
              dayOfWeek: day.value,
              startTime: "09:00",
              endTime: "17:00",
              timezone: "UTC",
              isActive: false,
            };
          });
          setWorkingHours(completeSchedule);
        }
      } else {
        throw new Error(result.error || "Failed to fetch working hours");
      }
    } catch (error) {
      console.error("Error fetching working hours:", error);
      toast.error("Failed to load working hours");
      initializeDefaultSchedule();
    } finally {
      setLoading(false);
    }
  }, [agentId, initializeDefaultSchedule]);

  // Save working hours
  const saveWorkingHours = async () => {
    setSaving(true);
    try {
      const schedule = workingHours
        .filter((wh) => wh.isActive)
        .map((wh) => ({
          dayOfWeek: wh.dayOfWeek,
          startTime: wh.startTime,
          endTime: wh.endTime,
          timezone: wh.timezone,
          isActive: wh.isActive,
        }));

      const response = await fetch(`/api/agents/${agentId}/working-hours`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ schedule }),
      });

      const result = await response.json();

      if (result.success) {
        toast.success("Working hours saved successfully");
        fetchWorkingHours(); // Refresh to get IDs
      } else {
        throw new Error(result.error || "Failed to save working hours");
      }
    } catch (error) {
      console.error("Error saving working hours:", error);
      toast.error("Failed to save working hours");
    } finally {
      setSaving(false);
    }
  };

  // Update working hours for a specific day
  const updateWorkingHours = (dayOfWeek: number, field: string, value: string | boolean) => {
    setWorkingHours((prev) =>
      prev.map((wh) =>
        wh.dayOfWeek === dayOfWeek ? { ...wh, [field]: value } : wh
      )
    );
  };

  useEffect(() => {
    fetchWorkingHours();
  }, [agentId, fetchWorkingHours]);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Working Hours
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Working Hours
        </CardTitle>
        <CardDescription>
          Set your weekly schedule and timezone preferences
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Global Timezone Setting */}
        <div className="space-y-2">
          <Label>Default Timezone</Label>
          <Select
            value={workingHours[0]?.timezone || "UTC"}
            onValueChange={(value) => {
              setWorkingHours((prev) =>
                prev.map((wh) => ({ ...wh, timezone: value }))
              );
            }}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {TIMEZONES.map((tz) => (
                <SelectItem key={tz.value} value={tz.value}>
                  {tz.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Daily Schedule */}
        <div className="space-y-4">
          <Label>Weekly Schedule</Label>
          {DAYS_OF_WEEK.map((day) => {
            const daySchedule = workingHours.find((wh) => wh.dayOfWeek === day.value);
            if (!daySchedule) return null;

            return (
              <div key={day.value} className="flex items-center gap-4 p-3 border rounded-lg">
                <div className="w-20">
                  <Switch
                    checked={daySchedule.isActive}
                    onCheckedChange={(checked) =>
                      updateWorkingHours(day.value, "isActive", checked)
                    }
                  />
                </div>
                <div className="w-24 font-medium">{day.label}</div>
                <div className="flex items-center gap-2">
                  <Input
                    type="time"
                    value={daySchedule.startTime}
                    onChange={(e) =>
                      updateWorkingHours(day.value, "startTime", e.target.value)
                    }
                    disabled={!daySchedule.isActive}
                    className="w-32"
                  />
                  <span className="text-muted-foreground">to</span>
                  <Input
                    type="time"
                    value={daySchedule.endTime}
                    onChange={(e) =>
                      updateWorkingHours(day.value, "endTime", e.target.value)
                    }
                    disabled={!daySchedule.isActive}
                    className="w-32"
                  />
                </div>
              </div>
            );
          })}
        </div>

        {/* Actions */}
        <div className="flex items-center gap-2">
          <Button onClick={saveWorkingHours} disabled={saving}>
            {saving ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Save Schedule
          </Button>
          <Button variant="outline" onClick={fetchWorkingHours}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Reset
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
