# Task 9: Agent Quality of Life Features

## 🎯 Objective

Implement essential quality of life improvements for agents including quick actions toolbar, task notes/comments system, @mentions functionality, and escalation requests to significantly enhance agent productivity and user experience.

## 📋 Status

- **Priority**: P2 (High Impact)
- **Status**: 🔄 Ready to Start
- **Estimated Time**: 6-8 hours
- **Dependencies**: Task 8 (Testing & Validation)

## 🔧 Technical Requirements

### Database Schema Updates

1. **Task Comments Table**
   ```sql
   CREATE TABLE task_comments (
     id UUID PRIMARY KEY,
     task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
     user_id UUID REFERENCES users(id),
     content TEXT NOT NULL,
     mentions JSONB DEFAULT '[]', -- Array of mentioned user IDs
     is_internal BOOLEAN DEFAULT true, -- Internal vs customer-facing
     created_at TIMESTAMP DEFAULT NOW(),
     updated_at TIMESTAMP DEFAULT NOW()
   );
   ```

2. **Task Notes Table**
   ```sql
   CREATE TABLE task_notes (
     id UUID PRIMARY KEY,
     task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
     user_id UUID REFERENCES users(id),
     content TEXT NOT NULL,
     is_private BOOLEAN DEFAULT false, -- Private to agent vs shared
     created_at TIMESTAMP DEFAULT NOW(),
     updated_at TIMESTAMP DEFAULT NOW()
   );
   ```

3. **Escalation Requests Table**
   ```sql
   CREATE TABLE escalation_requests (
     id UUID PRIMARY KEY,
     task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
     requested_by UUID REFERENCES users(id),
     escalated_to UUID REFERENCES users(id), -- Supervisor/Admin
     reason TEXT NOT NULL,
     status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, APPROVED, REJECTED
     comments TEXT,
     created_at TIMESTAMP DEFAULT NOW(),
     resolved_at TIMESTAMP NULL
   );
   ```

### Prisma Schema Updates

```prisma
model TaskComment {
  id        String   @id @default(cuid())
  taskId    String   @map("task_id")
  userId    String   @map("user_id")
  content   String
  mentions  Json     @default("[]")
  isInternal Boolean @default(true) @map("is_internal")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  
  task Task @relation(fields: [taskId], references: [id], onDelete: Cascade)
  user User @relation(fields: [userId], references: [id])
  
  @@map("task_comments")
}

model TaskNote {
  id        String   @id @default(cuid())
  taskId    String   @map("task_id")
  userId    String   @map("user_id")
  content   String
  isPrivate Boolean  @default(false) @map("is_private")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  
  task Task @relation(fields: [taskId], references: [id], onDelete: Cascade)
  user User @relation(fields: [userId], references: [id])
  
  @@map("task_notes")
}

model EscalationRequest {
  id          String    @id @default(cuid())
  taskId      String    @map("task_id")
  requestedBy String    @map("requested_by")
  escalatedTo String?   @map("escalated_to")
  reason      String
  status      String    @default("PENDING")
  comments    String?
  createdAt   DateTime  @default(now()) @map("created_at")
  resolvedAt  DateTime? @map("resolved_at")
  
  task        Task @relation(fields: [taskId], references: [id], onDelete: Cascade)
  requester   User @relation("EscalationRequester", fields: [requestedBy], references: [id])
  supervisor  User? @relation("EscalationSupervisor", fields: [escalatedTo], references: [id])
  
  @@map("escalation_requests")
}
```

## 🛠️ Implementation Steps

### Step 1: Database Schema Migration (30 minutes)

1. **Update Prisma Schema**
   - Add new models to `prisma/schema.prisma`
   - Add relations to existing Task and User models

2. **Create Migration**
   ```bash
   npx prisma migrate dev --name add-agent-qol-features
   ```

3. **Update Seed Data**
   - Add sample comments and notes to existing tasks
   - Create sample escalation requests

### Step 2: TypeScript Types (20 minutes)

1. **Update `lib/types.ts`**
   ```typescript
   export interface TaskComment {
     id: string;
     taskId: string;
     userId: string;
     content: string;
     mentions: string[];
     isInternal: boolean;
     createdAt: Date;
     updatedAt: Date;
     user: {
       id: string;
       name: string;
       email: string;
     };
   }

   export interface TaskNote {
     id: string;
     taskId: string;
     userId: string;
     content: string;
     isPrivate: boolean;
     createdAt: Date;
     updatedAt: Date;
     user: {
       id: string;
       name: string;
     };
   }

   export interface EscalationRequest {
     id: string;
     taskId: string;
     requestedBy: string;
     escalatedTo?: string;
     reason: string;
     status: 'PENDING' | 'APPROVED' | 'REJECTED';
     comments?: string;
     createdAt: Date;
     resolvedAt?: Date;
     requester: {
       id: string;
       name: string;
       email: string;
     };
     supervisor?: {
       id: string;
       name: string;
       email: string;
     };
   }
   ```

### Step 3: API Endpoints (90 minutes)

1. **Task Comments API** - `app/api/tasks/[id]/comments/route.ts`
   - GET: List task comments with pagination
   - POST: Create new comment with @mention processing

2. **Task Notes API** - `app/api/tasks/[id]/notes/route.ts`
   - GET: List task notes (filtered by privacy)
   - POST: Create new note
   - PATCH: Update existing note

3. **Escalation Requests API** - `app/api/tasks/[id]/escalate/route.ts`
   - POST: Create escalation request
   - GET: Get escalation status

4. **Quick Actions API** - `app/api/tasks/[id]/actions/route.ts`
   - POST: Handle quick actions (start, pause, complete, request help)

### Step 4: Enhanced Agent Dashboard Components (120 minutes)

1. **Quick Actions Toolbar** - `components/task-quick-actions.tsx`
   ```typescript
   interface QuickActionsProps {
     task: Task;
     onAction: (action: string) => void;
   }
   
   export function TaskQuickActions({ task, onAction }: QuickActionsProps) {
     // Start, Pause, Complete, Request Help buttons
     // Context-aware based on task status
   }
   ```

2. **Task Comments Component** - `components/task-comments.tsx`
   ```typescript
   export function TaskComments({ taskId }: { taskId: string }) {
     // Comments list with @mention highlighting
     // Rich text editor for new comments
     // @mention autocomplete functionality
   }
   ```

3. **Task Notes Component** - `components/task-notes.tsx`
   ```typescript
   export function TaskNotes({ taskId }: { taskId: string }) {
     // Private/shared notes toggle
     // Rich text editor
     // Notes history and editing
   }
   ```

4. **Escalation Dialog** - `components/escalation-dialog.tsx`
   ```typescript
   export function EscalationDialog({ taskId, isOpen, onClose }: EscalationDialogProps) {
     // Reason selection/input
     // Supervisor selection
     // One-click escalation
   }
   ```

### Step 5: Enhanced Agent Dashboard Integration (60 minutes)

1. **Update `components/agent-dashboard.tsx`**
   - Integrate quick actions toolbar
   - Add task details modal with comments/notes
   - Add escalation functionality

2. **Task Detail Modal** - `components/task-detail-modal.tsx`
   - Comprehensive task view
   - Tabbed interface (Details, Comments, Notes, History)
   - Quick actions integration

## 🧪 Testing Requirements

### Unit Tests
- Comments CRUD operations
- @mention parsing and notification
- Escalation request workflow
- Quick actions functionality

### Integration Tests
- API endpoints functionality
- Database relationships and constraints
- Real-time updates for comments/mentions

### Manual Testing
- Create and manage task comments
- Test @mention functionality
- Escalate tasks to supervisors
- Use quick actions toolbar

## 📝 Acceptance Criteria

- [ ] Agents can perform quick actions (Start, Pause, Complete, Request Help) from toolbar
- [ ] Task comments system with full CRUD functionality
- [ ] @mentions work with autocomplete and notifications
- [ ] Task notes system with private/shared options
- [ ] One-click escalation requests with supervisor notification
- [ ] Real-time updates for comments and mentions
- [ ] Mobile-responsive design for all new components
- [ ] Existing functionality remains unaffected

## 🔗 Dependencies for Next Tasks

This task enhances agent productivity and provides foundation for:
- Real-time notification system
- Advanced collaboration features
- Performance tracking improvements

## 📋 Deliverables

1. Updated Prisma schema with comments, notes, and escalation models
2. Database migration files
3. TypeScript types for new features
4. API endpoints for all new functionality
5. React components for quick actions, comments, notes, and escalation
6. Enhanced agent dashboard with integrated features
7. Unit tests for all new functionality
8. Updated seed data with sample content

**Previous Task**: [Task 8: Testing & Validation](./task8-testing-validation.md)

---

## 🎨 Detailed Feature Specifications

### Quick Actions Toolbar

**Visual Design:**
- Floating toolbar at top of task cards
- Context-aware buttons based on task status
- Keyboard shortcuts (S=Start, P=Pause, C=Complete, H=Help)
- Loading states and confirmation dialogs

**Actions:**
1. **Start Task** - Changes status to IN_PROGRESS, starts timer
2. **Pause Task** - Pauses timer, adds pause note
3. **Complete Task** - Marks complete, prompts for completion notes
4. **Request Help** - Opens escalation dialog with pre-filled help request

### Task Comments System

**Features:**
- Rich text editor with formatting (bold, italic, lists)
- @mention autocomplete with user search
- File attachments support
- Comment threading/replies
- Edit/delete permissions
- Internal vs customer-facing toggle

**@Mentions Implementation:**
```typescript
// Mention parsing regex
const MENTION_REGEX = /@\[([^\]]+)\]\(([^)]+)\)/g;

// Notification trigger
async function processMentions(content: string, taskId: string) {
  const mentions = extractMentions(content);
  for (const userId of mentions) {
    await createNotification({
      userId,
      type: 'MENTION',
      taskId,
      message: `You were mentioned in task comments`
    });
  }
}
```

### Task Notes System

**Features:**
- Private notes (visible only to author)
- Shared notes (visible to all team members)
- Rich text formatting
- Auto-save functionality
- Version history
- Search within notes

### Escalation Requests

**Workflow:**
1. Agent clicks "Request Help" or "Escalate"
2. Modal opens with reason categories:
   - Technical Issue
   - Customer Escalation
   - Policy Question
   - Resource Needed
   - Other (custom reason)
3. Auto-selects appropriate supervisor based on:
   - Task type
   - Agent's team
   - Supervisor availability
4. Sends notification to supervisor
5. Creates audit trail

**Supervisor Actions:**
- Approve escalation (reassign task)
- Provide guidance (add comment, keep assigned)
- Reject escalation (with explanation)

---

## 🔧 Technical Implementation Details

### Real-time Updates

```typescript
// WebSocket events for real-time features
enum SocketEvents {
  COMMENT_ADDED = "comment:added",
  MENTION_RECEIVED = "mention:received",
  ESCALATION_REQUESTED = "escalation:requested",
  TASK_ACTION_PERFORMED = "task:action_performed"
}

// Client-side real-time updates
useEffect(() => {
  socket.on(SocketEvents.COMMENT_ADDED, (data) => {
    if (data.taskId === currentTaskId) {
      setComments(prev => [...prev, data.comment]);
    }
  });

  socket.on(SocketEvents.MENTION_RECEIVED, (data) => {
    showNotification(`You were mentioned in ${data.taskTitle}`);
  });
}, []);
```

### Performance Optimizations

1. **Lazy Loading**: Comments/notes loaded on demand
2. **Pagination**: Large comment threads paginated
3. **Debounced Search**: @mention autocomplete with debouncing
4. **Optimistic Updates**: UI updates before API confirmation
5. **Caching**: Recent comments cached in localStorage

### Security Considerations

1. **Authorization**: Users can only see comments/notes they have access to
2. **Input Sanitization**: All user content sanitized to prevent XSS
3. **Rate Limiting**: API endpoints rate-limited to prevent spam
4. **Audit Trail**: All actions logged for compliance

---

## 📱 Mobile Responsiveness

### Mobile-First Design
- Touch-friendly quick action buttons
- Swipe gestures for common actions
- Collapsible comment threads
- Voice-to-text for comments/notes
- Offline comment drafts

### Progressive Web App Features
- Push notifications for mentions
- Offline comment viewing
- Background sync for drafts
- App-like navigation

---

## 🎯 Success Metrics

### User Experience Metrics
- Time to complete common actions (target: <3 seconds)
- Comment engagement rate (target: >60% of tasks)
- Escalation resolution time (target: <2 hours)
- Agent satisfaction score improvement (target: +20%)

### Technical Metrics
- API response times (target: <200ms)
- Real-time update latency (target: <1 second)
- Mobile usability score (target: >90)
- Accessibility compliance (WCAG 2.1 AA)

---

## 🚀 Future Enhancements

### Phase 1 Extensions
- Voice comments and transcription
- Smart comment suggestions
- Automated escalation triggers
- Integration with external tools (Slack, Teams)

### Advanced Features
- AI-powered comment sentiment analysis
- Predictive escalation recommendations
- Advanced search across all comments/notes
- Comment templates and snippets
