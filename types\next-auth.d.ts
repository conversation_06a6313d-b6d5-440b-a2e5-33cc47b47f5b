import { UserR<PERSON> } from "@prisma/client"
import { DefaultSession, DefaultUser } from "next-auth"
import { DefaultJWT } from "next-auth/jwt"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      role: UserRole
      organizationId: string
    } & DefaultSession["user"]
  }

  interface User extends DefaultUser {
    role: UserRole
    organizationId: string
  }
}

declare module "next-auth/jwt" {
  interface JWT extends DefaultJWT {
    role: UserRole
    organizationId: string
  }
}
