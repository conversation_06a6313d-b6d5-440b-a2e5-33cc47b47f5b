"use client";

import { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { 
  UserCheck, 
  Clock, 
  AlertCircle, 
  CheckCircle,
  RefreshCw,
  Calendar
} from "lucide-react";
import { toast } from "sonner";

interface AvailabilityStatus {
  id: string;
  agentId: string;
  status: 'available' | 'busy' | 'away' | 'offline';
  reason?: string;
  startTime?: string;
  endTime?: string;
  isTemporary: boolean;
}

interface AvailabilityManagerProps {
  agentId: string;
  currentStatus?: string;
  onStatusChange?: (status: string) => void;
}

const STATUS_OPTIONS = [
  { value: 'available', label: 'Available', icon: CheckCircle, color: 'bg-green-500' },
  { value: 'busy', label: 'Busy', icon: AlertCircle, color: 'bg-red-500' },
  { value: 'away', label: 'Away', icon: Clock, color: 'bg-yellow-500' },
  { value: 'offline', label: 'Offline', icon: UserCheck, color: 'bg-gray-500' },
];

export function AvailabilityManager({ 
  agentId, 
  currentStatus,
  onStatusChange 
}: AvailabilityManagerProps) {
  const [availability, setAvailability] = useState<AvailabilityStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  
  // Form state for setting temporary unavailability
  const [tempReason, setTempReason] = useState<string>('');
  const [tempDuration, setTempDuration] = useState<string>('60'); // minutes
  const [tempStartTime, setTempStartTime] = useState<string>('');
  const [tempEndTime, setTempEndTime] = useState<string>('');

  // Fetch current availability
  const fetchAvailability = useCallback(async () => {
    try {
      const response = await fetch(`/api/agents/${agentId}/availability`);
      const result = await response.json();

      if (result.success) {
        setAvailability(result.data.availability);
      } else {
        throw new Error(result.error || "Failed to fetch availability");
      }
    } catch (error) {
      console.error("Error fetching availability:", error);
      toast.error("Failed to load availability status");
    } finally {
      setLoading(false);
    }
  }, [agentId]);

  // Update availability status
  const updateAvailability = async (status: string, reason?: string, duration?: number) => {
    setUpdating(true);
    try {
      const response = await fetch(`/api/agents/${agentId}/availability`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ status, reason, duration }),
      });

      const result = await response.json();

      if (result.success) {
        toast.success(`Status updated to ${status}`);
        setAvailability(result.data);
        if (onStatusChange) onStatusChange(status);
        fetchAvailability(); // Refresh
      } else {
        throw new Error(result.error || "Failed to update availability");
      }
    } catch (error) {
      console.error("Error updating availability:", error);
      toast.error("Failed to update availability status");
    } finally {
      setUpdating(false);
    }
  };

  // Set temporary unavailability
  const setTemporaryUnavailability = async () => {
    if (!tempStartTime || !tempEndTime || !tempReason.trim()) {
      toast.error("Please fill in all fields for temporary unavailability");
      return;
    }

    setUpdating(true);
    try {
      const response = await fetch(`/api/agents/${agentId}/availability`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          startTime: tempStartTime,
          endTime: tempEndTime,
          reason: tempReason,
        }),
      });

      const result = await response.json();

      if (result.success) {
        toast.success("Temporary unavailability set successfully");
        setAvailability(result.data);
        setTempReason('');
        setTempStartTime('');
        setTempEndTime('');
        fetchAvailability();
      } else {
        throw new Error(result.error || "Failed to set temporary unavailability");
      }
    } catch (error) {
      console.error("Error setting temporary unavailability:", error);
      toast.error("Failed to set temporary unavailability");
    } finally {
      setUpdating(false);
    }
  };

  // Quick status update
  const quickStatusUpdate = async (status: string) => {
    if (status === 'away' || status === 'busy') {
      // For away/busy, use duration-based update
      const duration = parseInt(tempDuration) || 60;
      await updateAvailability(status, tempReason || `Set to ${status}`, duration);
    } else {
      // For available/offline, no duration needed
      await updateAvailability(status);
    }
  };

  useEffect(() => {
    fetchAvailability();
  }, [agentId, fetchAvailability]);

  // Set default times (now and 1 hour from now)
  useEffect(() => {
    const now = new Date();
    const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000);
    
    setTempStartTime(now.toISOString().slice(0, 16)); // YYYY-MM-DDTHH:MM
    setTempEndTime(oneHourLater.toISOString().slice(0, 16));
  }, []);

  const getStatusInfo = (status: string) => {
    return STATUS_OPTIONS.find(opt => opt.value === status) || STATUS_OPTIONS[0];
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserCheck className="h-5 w-5" />
            Availability Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const currentStatusInfo = getStatusInfo(availability?.status || currentStatus || 'available');

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <UserCheck className="h-5 w-5" />
          Availability Status
        </CardTitle>
        <CardDescription>
          Manage your current availability and set temporary periods
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current Status */}
        <div className="space-y-3">
          <Label>Current Status</Label>
          <div className="flex items-center gap-3">
            <Badge className={`${currentStatusInfo.color} text-white`}>
              <currentStatusInfo.icon className="h-3 w-3 mr-1" />
              {currentStatusInfo.label}
            </Badge>
            {availability?.reason && (
              <span className="text-sm text-muted-foreground">
                {availability.reason}
              </span>
            )}
            {availability?.isTemporary && availability?.endTime && (
              <span className="text-sm text-muted-foreground">
                Until {new Date(availability.endTime).toLocaleString()}
              </span>
            )}
          </div>
        </div>

        {/* Quick Status Update */}
        <div className="space-y-3">
          <Label>Quick Status Update</Label>
          <div className="grid grid-cols-2 gap-2">
            {STATUS_OPTIONS.map((status) => (
              <Button
                key={status.value}
                variant={availability?.status === status.value ? "default" : "outline"}
                size="sm"
                onClick={() => quickStatusUpdate(status.value)}
                disabled={updating}
                className="justify-start"
              >
                <status.icon className="h-4 w-4 mr-2" />
                {status.label}
              </Button>
            ))}
          </div>
        </div>

        {/* Temporary Status Settings */}
        <div className="space-y-3">
          <Label>Quick Away/Busy Settings</Label>
          <div className="grid grid-cols-2 gap-3">
            <div className="space-y-2">
              <Label className="text-sm">Duration (minutes)</Label>
              <Input
                type="number"
                value={tempDuration}
                onChange={(e) => setTempDuration(e.target.value)}
                placeholder="60"
                min="1"
                max="480"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-sm">Reason (optional)</Label>
              <Input
                value={tempReason}
                onChange={(e) => setTempReason(e.target.value)}
                placeholder="In a meeting"
              />
            </div>
          </div>
        </div>

        {/* Scheduled Unavailability */}
        <div className="space-y-3">
          <Label className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Schedule Unavailability
          </Label>
          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-2">
                <Label className="text-sm">Start Time</Label>
                <Input
                  type="datetime-local"
                  value={tempStartTime}
                  onChange={(e) => setTempStartTime(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label className="text-sm">End Time</Label>
                <Input
                  type="datetime-local"
                  value={tempEndTime}
                  onChange={(e) => setTempEndTime(e.target.value)}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label className="text-sm">Reason</Label>
              <Textarea
                value={tempReason}
                onChange={(e) => setTempReason(e.target.value)}
                placeholder="Meeting, appointment, break, etc."
                rows={2}
              />
            </div>
            <Button 
              onClick={setTemporaryUnavailability} 
              disabled={updating}
              variant="outline"
              size="sm"
            >
              {updating ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Calendar className="h-4 w-4 mr-2" />
              )}
              Schedule Unavailability
            </Button>
          </div>
        </div>

        {/* Refresh Button */}
        <Button variant="outline" onClick={fetchAvailability} size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh Status
        </Button>
      </CardContent>
    </Card>
  );
}
