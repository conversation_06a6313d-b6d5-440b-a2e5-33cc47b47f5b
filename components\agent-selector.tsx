"use client"

import { useState, useEffect } from "react"
import { Check, ChevronsUpDown, User, Zap, Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"
import { toast } from "sonner"

interface Agent {
  id: string
  name: string
  email: string
  status: "AVAILABLE" | "BUSY" | "AWAY" | "OFFLINE"
  currentTaskCount: number
  maxConcurrentTasks: number
  utilizationRate: number
}

interface AgentSelectorProps {
  value?: string
  onValueChange: (value: string | undefined) => void
  placeholder?: string
  disabled?: boolean
  taskId?: string // Add taskId for auto-assignment
  showAutoAssign?: boolean // Option to show/hide auto-assign button
}

export function AgentSelector({
  value,
  onValueChange,
  placeholder = "Select agent...",
  disabled = false,
  taskId,
  showAutoAssign = true
}: AgentSelectorProps) {
  const [open, setOpen] = useState(false)
  const [agents, setAgents] = useState<Agent[]>([])
  const [loading, setLoading] = useState(false)
  const [autoAssigning, setAutoAssigning] = useState(false)

  useEffect(() => {
    fetchAgents()
  }, [])

  const fetchAgents = async () => {
    setLoading(true)
    try {
      const response = await fetch("/api/agents")
      const result = await response.json()

      if (result.success && result.data && Array.isArray(result.data.agents)) {
        setAgents(result.data.agents)
      } else {
        console.error("Failed to fetch agents:", result.error)
        setAgents([]) // Ensure agents is always an array
      }
    } catch (error) {
      console.error("Error fetching agents:", error)
      setAgents([]) // Ensure agents is always an array
    } finally {
      setLoading(false)
    }
  }

  const selectedAgent = agents?.find((agent) => agent.id === value)

  const getStatusColor = (status: string) => {
    switch (status) {
      case "AVAILABLE":
        return "bg-green-500"
      case "BUSY":
        return "bg-yellow-500"
      case "AWAY":
        return "bg-orange-500"
      case "OFFLINE":
        return "bg-gray-500"
      default:
        return "bg-gray-500"
    }
  }

  const getUtilizationColor = (rate: number) => {
    if (rate >= 90) return "text-red-600"
    if (rate >= 70) return "text-yellow-600"
    return "text-green-600"
  }

  const isAgentAvailable = (agent: Agent) => {
    return agent?.status === "AVAILABLE" && (agent?.currentTaskCount || 0) < (agent?.maxConcurrentTasks || 0)
  }

  const handleAutoAssign = async () => {
    if (!taskId) {
      toast.error("Task ID is required for auto-assignment")
      return
    }

    setAutoAssigning(true)
    try {
      const response = await fetch(`/api/tasks/${taskId}/assign`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          strategy: 'hybrid_intelligent' // Use the best available strategy
        })
      })

      const result = await response.json()

      if (result.success && result.data?.assignedUser) {
        const assignedAgent = result.data.assignedUser
        onValueChange(assignedAgent.id)
        toast.success(`Task auto-assigned to ${assignedAgent.name}`)

        // Refresh agents list to update task counts
        await fetchAgents()
      } else {
        toast.error(result.error?.message || "Failed to auto-assign task")
      }
    } catch (error) {
      console.error("Auto-assignment error:", error)
      toast.error("Failed to auto-assign task")
    } finally {
      setAutoAssigning(false)
    }
  }

  return (
    <div className="flex gap-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="flex-1 justify-between"
            disabled={disabled}
          >
            {selectedAgent ? (
              <div className="flex items-center gap-2">
                <div className={cn("w-2 h-2 rounded-full", getStatusColor(selectedAgent.status))} />
                <span>{selectedAgent.name}</span>
                <Badge variant="secondary" className="text-xs">
                  {selectedAgent.currentTaskCount}/{selectedAgent.maxConcurrentTasks}
                </Badge>
              </div>
            ) : (
              <span className="text-muted-foreground">{placeholder}</span>
            )}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
      <PopoverContent className="w-full p-0">
        <Command>
          <CommandInput placeholder="Search agents..." />
          <CommandEmpty>
            {loading ? "Loading agents..." : "No agents found."}
          </CommandEmpty>
          <CommandGroup>
            {/* Unassign option */}
            <CommandItem
              value="unassign"
              onSelect={() => {
                onValueChange(undefined)
                setOpen(false)
              }}
            >
              <Check
                className={cn(
                  "mr-2 h-4 w-4",
                  !value ? "opacity-100" : "opacity-0"
                )}
              />
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-muted-foreground" />
                <span>Unassigned</span>
              </div>
            </CommandItem>
            
            {/* Available agents first */}
            {agents
              ?.filter(isAgentAvailable)
              ?.map((agent) => (
                <CommandItem
                  key={agent.id}
                  value={agent.id}
                  onSelect={(currentValue) => {
                    onValueChange(currentValue === value ? undefined : currentValue)
                    setOpen(false)
                  }}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === agent.id ? "opacity-100" : "opacity-0"
                    )}
                  />
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-2">
                      <div className={cn("w-2 h-2 rounded-full", getStatusColor(agent.status))} />
                      <div>
                        <div className="font-medium">{agent.name}</div>
                        <div className="text-xs text-muted-foreground">{agent.email}</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        {agent.currentTaskCount}/{agent.maxConcurrentTasks}
                      </Badge>
                      <span className={cn("text-xs font-medium", getUtilizationColor(agent.utilizationRate))}>
                        {Math.round(agent.utilizationRate)}%
                      </span>
                    </div>
                  </div>
                </CommandItem>
              ))}
            
            {/* Unavailable agents */}
            {agents
              ?.filter(agent => !isAgentAvailable(agent))
              ?.map((agent) => (
                <CommandItem
                  key={agent.id}
                  value={agent.id}
                  onSelect={(currentValue) => {
                    onValueChange(currentValue === value ? undefined : currentValue)
                    setOpen(false)
                  }}
                  disabled
                  className="opacity-50"
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === agent.id ? "opacity-100" : "opacity-0"
                    )}
                  />
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-2">
                      <div className={cn("w-2 h-2 rounded-full", getStatusColor(agent.status))} />
                      <div>
                        <div className="font-medium">{agent.name}</div>
                        <div className="text-xs text-muted-foreground">{agent.email}</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        {agent.currentTaskCount}/{agent.maxConcurrentTasks}
                      </Badge>
                      <span className={cn("text-xs font-medium", getUtilizationColor(agent.utilizationRate))}>
                        {Math.round(agent.utilizationRate)}%
                      </span>
                      <Badge variant="outline" className="text-xs">
                        {agent.status}
                      </Badge>
                    </div>
                  </div>
                </CommandItem>
              ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
      </Popover>

    {/* Auto Assign Button */}
    {showAutoAssign && taskId && (
      <Button
        variant="secondary"
        onClick={handleAutoAssign}
        disabled={disabled || autoAssigning}
        className="shrink-0"
        title="Automatically assign to the best available agent"
      >
        {autoAssigning ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <Zap className="h-4 w-4" />
        )}
        <span className="ml-1 hidden sm:inline">Auto</span>
      </Button>
    )}
    </div>
  )
}
