import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { createSkillsManager } from '@/lib/skills-utils';
import { AddTaskSkillRequest } from '@/lib/types';

/**
 * GET /api/tasks/[id]/skills
 * Get all skill requirements for a specific task
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const taskId = params.id;

    const skillsManager = createSkillsManager();
    const requirements = await skillsManager.getTaskSkillRequirements(taskId);

    return NextResponse.json({
      success: true,
      data: requirements,
    });
  } catch (error) {
    console.error('Error fetching task skill requirements:', error);
    return NextResponse.json(
      { error: 'Failed to fetch task skill requirements' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/tasks/[id]/skills
 * Add a skill requirement to a task
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const taskId = params.id;

    // Only admins can add skill requirements to tasks
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body: AddTaskSkillRequest = await request.json();

    // Validate required fields
    if (!body.skillName || body.skillName.trim().length === 0) {
      return NextResponse.json(
        { error: 'Skill name is required' },
        { status: 400 }
      );
    }

    if (!body.requiredLevel || body.requiredLevel < 1 || body.requiredLevel > 5) {
      return NextResponse.json(
        { error: 'Required level must be between 1 and 5' },
        { status: 400 }
      );
    }

    const skillsManager = createSkillsManager();
    const requirement = await skillsManager.addSkillRequirementToTask(
      taskId,
      body.skillName.trim(),
      body.requiredLevel
    );

    return NextResponse.json({
      success: true,
      data: requirement,
    }, { status: 201 });
  } catch (error: unknown) {
    console.error('Error adding task skill requirement:', error);

    if (error instanceof Error && error.message.includes('already requires skill')) {
      return NextResponse.json(
        { error: error.message },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to add skill requirement to task' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/tasks/[id]/skills
 * Update a task's skill requirement level
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const taskId = params.id;

    // Only admins can update task skill requirements
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const { skillName, requiredLevel } = body;

    // Validate required fields
    if (!skillName || skillName.trim().length === 0) {
      return NextResponse.json(
        { error: 'Skill name is required' },
        { status: 400 }
      );
    }

    if (!requiredLevel || requiredLevel < 1 || requiredLevel > 5) {
      return NextResponse.json(
        { error: 'Required level must be between 1 and 5' },
        { status: 400 }
      );
    }

    // For now, we'll implement this by removing and re-adding
    // In a real implementation, you'd add an updateTaskSkillRequirement method
    const skillsManager = createSkillsManager();
    
    // Remove existing requirement
    await skillsManager.removeSkillRequirementFromTask(taskId, skillName.trim());
    
    // Add new requirement with updated level
    const requirement = await skillsManager.addSkillRequirementToTask(
      taskId,
      skillName.trim(),
      requiredLevel
    );

    return NextResponse.json({
      success: true,
      data: requirement,
    });
  } catch (error: unknown) {
    console.error('Error updating task skill requirement:', error);
    return NextResponse.json(
      { error: 'Failed to update task skill requirement' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/tasks/[id]/skills
 * Remove a skill requirement from a task
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const taskId = params.id;

    // Only admins can remove task skill requirements
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const skillName = searchParams.get('skillName');

    if (!skillName) {
      return NextResponse.json(
        { error: 'Skill name is required' },
        { status: 400 }
      );
    }

    const skillsManager = createSkillsManager();
    const removed = await skillsManager.removeSkillRequirementFromTask(taskId, skillName);

    if (!removed) {
      return NextResponse.json(
        { error: 'Skill requirement not found for this task' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Skill requirement removed successfully',
    });
  } catch (error) {
    console.error('Error removing task skill requirement:', error);
    return NextResponse.json(
      { error: 'Failed to remove task skill requirement' },
      { status: 500 }
    );
  }
}


