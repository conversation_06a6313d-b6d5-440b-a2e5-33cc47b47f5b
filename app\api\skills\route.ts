import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { createSkillsManager } from '@/lib/skills-utils';
import { CreateSkillRequest } from '@/lib/types';

/**
 * GET /api/skills
 * Get all skills in the organization catalog
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const activeOnly = searchParams.get('active') !== 'false';
    const category = searchParams.get('category');

    const skillsManager = createSkillsManager();
    let skills = await skillsManager.getOrganizationSkills(
      session.user.organizationId,
      activeOnly
    );

    // Filter by category if specified
    if (category) {
      skills = skills.filter(skill => skill.category === category);
    }

    return NextResponse.json({
      success: true,
      data: skills,
    });
  } catch (error) {
    console.error('Error fetching skills:', error);
    return NextResponse.json(
      { error: 'Failed to fetch skills' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/skills
 * Create a new skill in the organization catalog
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only admins can create skills
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body: CreateSkillRequest = await request.json();

    // Validate required fields
    if (!body.name || body.name.trim().length === 0) {
      return NextResponse.json(
        { error: 'Skill name is required' },
        { status: 400 }
      );
    }

    const skillsManager = createSkillsManager();
    const skill = await skillsManager.createSkill(
      session.user.organizationId,
      {
        name: body.name.trim(),
        description: body.description?.trim(),
        category: body.category?.trim(),
      }
    );

    return NextResponse.json({
      success: true,
      data: skill,
    }, { status: 201 });
  } catch (error: unknown) {
    console.error('Error creating skill:', error);

    if (error instanceof Error && error.message.includes('already exists')) {
      return NextResponse.json(
        { error: error.message },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create skill' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/skills/[id]
 * Update a skill in the catalog
 */
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only admins can update skills
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const skillId = searchParams.get('id');

    if (!skillId) {
      return NextResponse.json(
        { error: 'Skill ID is required' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { action } = body;

    const skillsManager = createSkillsManager();

    if (action === 'deactivate') {
      const skill = await skillsManager.deactivateSkill(skillId);
      return NextResponse.json({
        success: true,
        data: skill,
      });
    }

    // For other updates, you would implement updateSkill method
    return NextResponse.json(
      { error: 'Update action not implemented yet' },
      { status: 501 }
    );
  } catch (error) {
    console.error('Error updating skill:', error);
    return NextResponse.json(
      { error: 'Failed to update skill' },
      { status: 500 }
    );
  }
}
