"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { AdminDashboard } from "@/components/admin-dashboard"
import { AgentDashboard } from "@/components/agent-dashboard"
import { BarChart3, Users } from "lucide-react"

export default function DashboardPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            Task Routing Platform
          </h1>
          <p className="text-gray-600">
            Manage tasks, agents, and monitor system performance
          </p>
        </div>

        {/* Main Dashboard Tabs */}
        <Tabs defaultValue="admin" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="admin" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Admin Dashboard
            </TabsTrigger>
            <TabsTrigger value="agent" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Agent Dashboard
            </TabsTrigger>
          </TabsList>

          <TabsContent value="admin">
            <AdminDashboard />
          </TabsContent>

          <TabsContent value="agent">
            <AgentDashboard />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
