# Task 3: Working Hours & Availability System

## 🎯 Objective

Implement a comprehensive working hours and availability system that respects agent schedules, time zones, and availability status for intelligent task routing.

## 📋 Status

- **Priority**: P2 (Important)
- **Status**: ⏳ Waiting for Task 1
- **Estimated Time**: 4-5 hours
- **Dependencies**: Task 1 (Skills Management)

## 🔧 Technical Requirements

### Database Schema Updates

```prisma
model AgentWorkingHours {
  id        String   @id @default(cuid())
  agentId   String   @map("agent_id")
  dayOfWeek Int      @map("day_of_week") // 0=Sunday, 1=Monday, etc.
  startTime String   @map("start_time")  // "09:00"
  endTime   String   @map("end_time")    // "17:00"
  timezone  String   @default("UTC")
  isActive  Boolean  @default(true) @map("is_active")
  createdAt DateTime @default(now()) @map("created_at")
  
  agent User @relation(fields: [agentId], references: [id], onDelete: Cascade)
  
  @@unique([agentId, dayOfWeek])
  @@map("agent_working_hours")
}

model AgentAvailability {
  id          String    @id @default(cuid())
  agentId     String    @map("agent_id")
  status      String    @default("available") // 'available', 'busy', 'away', 'offline'
  reason      String?   // Optional reason for unavailability
  startTime   DateTime? @map("start_time")    // For temporary status changes
  endTime     DateTime? @map("end_time")      // When status should revert
  isTemporary Boolean   @default(false) @map("is_temporary")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  
  agent User @relation(fields: [agentId], references: [id], onDelete: Cascade)
  
  @@map("agent_availability")
}

model OrganizationSettings {
  id                    String   @id @default(cuid())
  organizationId        String   @unique @map("organization_id")
  defaultTimezone       String   @default("UTC") @map("default_timezone")
  businessHoursStart    String   @default("09:00") @map("business_hours_start")
  businessHoursEnd      String   @default("17:00") @map("business_hours_end")
  businessDays          Json     @default("[1,2,3,4,5]") // Monday-Friday
  afterHoursRouting     Boolean  @default(false) @map("after_hours_routing")
  weekendRouting        Boolean  @default(false) @map("weekend_routing")
  urgentTasksOverride   Boolean  @default(true) @map("urgent_tasks_override")
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")
  
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  @@map("organization_settings")
}
```

### TypeScript Types

```typescript
export interface WorkingHours {
  id: string;
  agentId: string;
  dayOfWeek: number;
  startTime: string;
  endTime: string;
  timezone: string;
  isActive: boolean;
}

export interface AvailabilityStatus {
  id: string;
  agentId: string;
  status: 'available' | 'busy' | 'away' | 'offline';
  reason?: string;
  startTime?: Date;
  endTime?: Date;
  isTemporary: boolean;
}

export interface OrganizationWorkingSettings {
  id: string;
  organizationId: string;
  defaultTimezone: string;
  businessHoursStart: string;
  businessHoursEnd: string;
  businessDays: number[];
  afterHoursRouting: boolean;
  weekendRouting: boolean;
  urgentTasksOverride: boolean;
}
```

## 🛠️ Implementation Steps

### Step 1: Database Schema Updates (30 minutes)

1. **Update Prisma Schema**
   - Add working hours, availability, and organization settings models
   - Update User model to include relations

2. **Create Migration**
   ```bash
   npx prisma migrate dev --name add-working-hours-availability
   ```

3. **Update Seed Data**
   - Add working hours for demo agents
   - Set default availability status
   - Configure organization settings

### Step 2: Working Hours Management (90 minutes)

1. **Create `lib/working-hours-manager.ts`**
   ```typescript
   export class WorkingHoursManager {
     // Working hours CRUD
     async setAgentWorkingHours(agentId: string, schedule: WorkingHours[]) {
       // Set weekly schedule for agent
     }
     
     async getAgentWorkingHours(agentId: string): Promise<WorkingHours[]> {
       // Get agent's weekly schedule
     }
     
     async updateWorkingDay(agentId: string, dayOfWeek: number, startTime: string, endTime: string) {
       // Update specific day schedule
     }
     
     // Availability management
     async setAgentAvailability(agentId: string, status: string, reason?: string, duration?: number) {
       // Set current availability status
     }
     
     async getAgentAvailability(agentId: string): Promise<AvailabilityStatus> {
       // Get current availability
     }
     
     async setTemporaryUnavailability(agentId: string, startTime: Date, endTime: Date, reason: string) {
       // Set temporary unavailability period
     }
     
     // Availability checking
     async isAgentAvailable(agentId: string, checkTime?: Date): Promise<boolean> {
       // Check if agent is available at specific time
     }
     
     async isWithinWorkingHours(agentId: string, checkTime?: Date): Promise<boolean> {
       // Check if time falls within agent's working hours
     }
     
     async getAvailableAgents(organizationId: string, checkTime?: Date): Promise<string[]> {
       // Get list of currently available agents
     }
     
     // Timezone utilities
     async convertToAgentTimezone(agentId: string, utcTime: Date): Promise<Date> {
       // Convert UTC time to agent's timezone
     }
     
     async getNextAvailableTime(agentId: string, fromTime?: Date): Promise<Date | null> {
       // Find next time agent will be available
     }
   }
   ```

### Step 3: Availability Checking Logic (60 minutes)

1. **Core Availability Algorithm**
   ```typescript
   async isAgentAvailable(agentId: string, checkTime: Date = new Date()): Promise<boolean> {
     // 1. Check current availability status
     const availability = await this.getAgentAvailability(agentId);
     if (availability.status !== 'available') {
       // Check if temporary status has expired
       if (availability.isTemporary && availability.endTime && checkTime > availability.endTime) {
         await this.setAgentAvailability(agentId, 'available');
         return true;
       }
       return false;
     }
     
     // 2. Check working hours
     const isInWorkingHours = await this.isWithinWorkingHours(agentId, checkTime);
     if (!isInWorkingHours) {
       // Check organization settings for after-hours routing
       const orgSettings = await this.getOrganizationSettings(agentId);
       if (!orgSettings.afterHoursRouting) return false;
     }
     
     // 3. Check capacity
     const agent = await prisma.user.findUnique({
       where: { id: agentId },
       select: { currentTaskCount: true, maxConcurrentTasks: true }
     });
     
     return agent.currentTaskCount < agent.maxConcurrentTasks;
   }
   ```

2. **Working Hours Validation**
   ```typescript
   async isWithinWorkingHours(agentId: string, checkTime: Date): Promise<boolean> {
     const agent = await prisma.user.findUnique({
       where: { id: agentId },
       include: { workingHours: true }
     });
     
     if (!agent || agent.workingHours.length === 0) {
       // No schedule set, use organization defaults
       return this.isWithinOrganizationHours(agent.organizationId, checkTime);
     }
     
     // Convert check time to agent's timezone
     const agentTime = await this.convertToAgentTimezone(agentId, checkTime);
     const dayOfWeek = agentTime.getDay();
     const timeString = agentTime.toTimeString().substring(0, 5); // "HH:MM"
     
     const daySchedule = agent.workingHours.find(wh => 
       wh.dayOfWeek === dayOfWeek && wh.isActive
     );
     
     if (!daySchedule) return false;
     
     return timeString >= daySchedule.startTime && timeString <= daySchedule.endTime;
   }
   ```

### Step 4: API Endpoints (75 minutes)

1. **Working Hours API** - `app/api/agents/[id]/working-hours/route.ts`
   ```typescript
   // GET: Get agent working hours
   // POST: Set agent working hours
   // PATCH: Update specific day
   ```

2. **Availability API** - `app/api/agents/[id]/availability/route.ts`
   ```typescript
   // GET: Get current availability status
   // POST: Set availability status
   // PATCH: Update availability with duration
   ```

3. **Organization Settings API** - `app/api/organizations/[id]/settings/route.ts`
   ```typescript
   // GET: Get organization working settings
   // PATCH: Update organization settings
   ```

4. **Availability Check API** - `app/api/agents/availability-check/route.ts`
   ```typescript
   // POST: Bulk check agent availability
   // Query: Get available agents for organization
   ```

### Step 5: Integration with Routing Engine (45 minutes)

1. **Update Enhanced Task Router**
   ```typescript
   async getEligibleAgents(task: TaskWithSkills): Promise<AgentWithSkills[]> {
     const allAgents = await this.getAllAgentsWithSkills(task.organizationId);
     const workingHoursManager = new WorkingHoursManager();
     
     const eligibleAgents = [];
     
     for (const agent of allAgents) {
       // Check availability (includes working hours, status, and capacity)
       const isAvailable = await workingHoursManager.isAgentAvailable(agent.id);
       if (!isAvailable) continue;
       
       // Check skills (from Task 1)
       if (task.requiredSkills.length > 0) {
         const hasRequiredSkills = this.checkSkillRequirements(agent, task);
         if (!hasRequiredSkills) continue;
       }
       
       eligibleAgents.push(agent);
     }
     
     return eligibleAgents;
   }
   ```

2. **Handle Urgent Tasks Override**
   ```typescript
   async handleUrgentTaskRouting(task: TaskWithSkills): Promise<AgentWithSkills[]> {
     if (task.priority !== 'urgent') return this.getEligibleAgents(task);
     
     const orgSettings = await this.getOrganizationSettings(task.organizationId);
     if (!orgSettings.urgentTasksOverride) return this.getEligibleAgents(task);
     
     // For urgent tasks, include agents even if outside working hours
     const allAgents = await this.getAllAgentsWithSkills(task.organizationId);
     return allAgents.filter(agent => 
       agent.status === 'available' && 
       agent.currentTaskCount < agent.maxConcurrentTasks
     );
   }
   ```

## 🧪 Testing Requirements

### Unit Tests
- Working hours validation logic
- Availability status management
- Timezone conversion utilities
- Urgent task override logic

### Integration Tests
- API endpoints functionality
- Database relationships and constraints
- Integration with routing engine

### Manual Testing
- Set agent working hours across different timezones
- Test availability status changes
- Verify urgent task routing overrides
- Check after-hours routing settings

## 📝 Acceptance Criteria

- [x] Agents can set weekly working hours with timezone support
- [x] Availability status can still be managed (available, busy, away, offline)
- [x] Temporary unavailability periods can be set
- [x] Task routing respects working hours and availability
- [x] Organization settings control after-hours and weekend routing
- [x] Urgent tasks can override working hours restrictions
- [x] Timezone conversions work correctly
- [x] API endpoints for managing schedules and availability

## 🔗 Dependencies for Next Tasks

This task enables:
- **Task 4**: Routing rules can include time-based conditions
- **Task 5**: SLA escalation considers working hours
- **Task 7**: UI components for schedule management

## 📋 Deliverables

1. Working hours database schema and models
2. Availability management system
3. Timezone handling utilities
4. API endpoints for schedule and availability management
5. Integration with enhanced routing engine
6. Organization-level working settings
7. Urgent task override functionality
8. Comprehensive unit tests

**Next Task**: [Task 4: Routing Rules Engine](./task4-routing-rules.md)
