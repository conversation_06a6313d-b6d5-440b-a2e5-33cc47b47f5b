import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { createWorkingHoursManager } from "@/lib/working-hours-manager";
import { AvailabilityCheckRequest } from "@/lib/types";
import { prisma } from "@/lib/prisma";

/**
 * POST /api/agents/availability-check
 * Bulk check agent availability
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body: AvailabilityCheckRequest = await request.json();

    // Validate request body
    if (!body.agentIds || !Array.isArray(body.agentIds)) {
      return NextResponse.json(
        { error: "agentIds must be an array" },
        { status: 400 }
      );
    }

    if (body.agentIds.length === 0) {
      return NextResponse.json(
        { error: "agentIds cannot be empty" },
        { status: 400 }
      );
    }

    // Verify all agents belong to the user's organization
    const agents = await prisma.user.findMany({
      where: {
        id: { in: body.agentIds },
        organizationId: session.user.organizationId,
      },
      select: { id: true },
    });

    if (agents.length !== body.agentIds.length) {
      return NextResponse.json(
        { error: "Some agents not found or not accessible" },
        { status: 400 }
      );
    }

    const checkTime = body.checkTime ? new Date(body.checkTime) : new Date();

    if (body.checkTime && isNaN(checkTime.getTime())) {
      return NextResponse.json(
        { error: "Invalid checkTime format" },
        { status: 400 }
      );
    }

    const workingHoursManager = createWorkingHoursManager();
    const results = await workingHoursManager.checkAgentsAvailability(
      body.agentIds,
      checkTime
    );

    return NextResponse.json({
      success: true,
      data: results,
      checkTime: checkTime.toISOString(),
    });
  } catch (error) {
    console.error("Error checking agent availability:", error);
    return NextResponse.json(
      { error: "Failed to check agent availability" },
      { status: 500 }
    );
  }
}

/**
 * GET /api/agents/availability-check
 * Get available agents for organization
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const checkTimeParam = searchParams.get("checkTime");
    const checkTime = checkTimeParam ? new Date(checkTimeParam) : new Date();

    if (checkTimeParam && isNaN(checkTime.getTime())) {
      return NextResponse.json(
        { error: "Invalid checkTime format" },
        { status: 400 }
      );
    }

    const workingHoursManager = createWorkingHoursManager();
    const availableAgentIds = await workingHoursManager.getAvailableAgents(
      session.user.organizationId,
      checkTime
    );

    // Get agent details
    const agents = await prisma.user.findMany({
      where: {
        id: { in: availableAgentIds },
        organizationId: session.user.organizationId,
      },
      select: {
        id: true,
        name: true,
        email: true,
        currentTaskCount: true,
        maxConcurrentTasks: true,
        status: true,
      },
    });

    return NextResponse.json({
      success: true,
      data: {
        availableAgents: agents,
        checkTime: checkTime.toISOString(),
        totalAvailable: agents.length,
      },
    });
  } catch (error) {
    console.error("Error getting available agents:", error);
    return NextResponse.json(
      { error: "Failed to get available agents" },
      { status: 500 }
    );
  }
}
