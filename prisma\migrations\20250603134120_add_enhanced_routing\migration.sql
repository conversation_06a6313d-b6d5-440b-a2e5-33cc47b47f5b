/*
  Warnings:

  - You are about to drop the column `responseDeadline` on the `tasks` table. All the data in the column will be lost.
  - You are about to drop the column `slaDeadline` on the `tasks` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "task_events" ADD COLUMN     "escalation_id" TEXT;

-- AlterTable
ALTER TABLE "tasks" DROP COLUMN "responseDeadline",
DROP COLUMN "slaDeadline",
ADD COLUMN     "applied_rules" JSONB,
ADD COLUMN     "breached_sla" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "metadata" JSONB DEFAULT '{}',
ADD COLUMN     "resolve_by" TIMESTAMP(3),
ADD COLUMN     "resolved_at" TIMESTAMP(3),
ADD COLUMN     "responded_at" TIMESTAMP(3),
ADD COLUMN     "response_by" TIMESTAMP(3),
ADD COLUMN     "sla_policy_id" TEXT;

-- AlterTable
ALTER TABLE "users" ADD COLUMN     "default_timezone" TEXT;

-- CreateTable
CREATE TABLE "agent_skills" (
    "id" TEXT NOT NULL,
    "agent_id" TEXT NOT NULL,
    "skill_name" TEXT NOT NULL,
    "proficiency_level" INTEGER NOT NULL DEFAULT 1,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "agent_skills_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "task_required_skills" (
    "id" TEXT NOT NULL,
    "task_id" TEXT NOT NULL,
    "skill_name" TEXT NOT NULL,
    "required_level" INTEGER NOT NULL DEFAULT 1,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "task_required_skills_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "skills_catalog" (
    "id" TEXT NOT NULL,
    "organization_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "category" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "skills_catalog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "agent_availability" (
    "id" TEXT NOT NULL,
    "agent_id" TEXT NOT NULL,
    "status" "UserStatus" NOT NULL,
    "status_message" TEXT,
    "start_time" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "end_time" TIMESTAMP(3),
    "is_scheduled" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "agent_availability_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "agent_metrics" (
    "id" TEXT NOT NULL,
    "agent_id" TEXT NOT NULL,
    "period" TEXT NOT NULL,
    "start_date" TIMESTAMP(3) NOT NULL,
    "end_date" TIMESTAMP(3) NOT NULL,
    "tasks_assigned" INTEGER NOT NULL DEFAULT 0,
    "tasks_completed" INTEGER NOT NULL DEFAULT 0,
    "tasks_escalated" INTEGER NOT NULL DEFAULT 0,
    "total_work_time" INTEGER NOT NULL DEFAULT 0,
    "active_time" INTEGER NOT NULL DEFAULT 0,
    "avg_response_time" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "avg_handle_time" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "completion_rate" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "quality_score" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "sla_compliance_rate" DOUBLE PRECISION NOT NULL DEFAULT 0,

    CONSTRAINT "agent_metrics_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "escalations" (
    "id" TEXT NOT NULL,
    "task_id" TEXT NOT NULL,
    "level" INTEGER NOT NULL,
    "reason" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "escalated_to" TEXT,
    "escalated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "resolved_at" TIMESTAMP(3),
    "comment" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "escalations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "routing_rules" (
    "id" TEXT NOT NULL,
    "organization_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "priority" INTEGER NOT NULL DEFAULT 0,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "conditions" JSONB NOT NULL,
    "actions" JSONB NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "routing_rules_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "routing_strategies" (
    "id" TEXT NOT NULL,
    "organization_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "strategy" TEXT NOT NULL,
    "is_default" BOOLEAN NOT NULL DEFAULT false,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "configuration" JSONB NOT NULL DEFAULT '{}',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "routing_strategies_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "rule_executions" (
    "id" TEXT NOT NULL,
    "rule_id" TEXT NOT NULL,
    "task_id" TEXT NOT NULL,
    "succeeded" BOOLEAN NOT NULL DEFAULT true,
    "details" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "rule_executions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "sla_policies" (
    "id" TEXT NOT NULL,
    "organization_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "priority" TEXT NOT NULL,
    "response_time" INTEGER NOT NULL,
    "resolution_time" INTEGER NOT NULL,
    "escalationRules" JSONB NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "sla_policies_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "task_metrics" (
    "id" TEXT NOT NULL,
    "task_id" TEXT NOT NULL,
    "assigned_agent_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "assigned_at" TIMESTAMP(3),
    "first_response_at" TIMESTAMP(3),
    "resolved_at" TIMESTAMP(3),
    "wait_time" INTEGER,
    "handle_time" INTEGER,
    "total_time" INTEGER,
    "sla_policy" TEXT,
    "response_target" INTEGER,
    "resolve_target" INTEGER,
    "met_response_sla" BOOLEAN,
    "met_resolve_sla" BOOLEAN,
    "complexity" INTEGER,
    "quality" INTEGER,
    "customer_rating" INTEGER,

    CONSTRAINT "task_metrics_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "team_metrics" (
    "id" TEXT NOT NULL,
    "organization_id" TEXT NOT NULL,
    "period" TEXT NOT NULL,
    "start_date" TIMESTAMP(3) NOT NULL,
    "end_date" TIMESTAMP(3) NOT NULL,
    "total_tasks" INTEGER NOT NULL DEFAULT 0,
    "completed_tasks" INTEGER NOT NULL DEFAULT 0,
    "escalated_tasks" INTEGER NOT NULL DEFAULT 0,
    "avg_response_time" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "avg_handle_time" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "sla_compliance_rate" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "customer_satisfaction" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "active_agents" INTEGER NOT NULL DEFAULT 0,
    "utilization_rate" DOUBLE PRECISION NOT NULL DEFAULT 0,

    CONSTRAINT "team_metrics_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "working_hours" (
    "id" TEXT NOT NULL,
    "agent_id" TEXT NOT NULL,
    "dayOfWeek" INTEGER NOT NULL,
    "startTime" TEXT NOT NULL,
    "endTime" TEXT NOT NULL,
    "timeZone" TEXT NOT NULL DEFAULT 'UTC',
    "is_work_day" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "working_hours_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "agent_performance" (
    "id" TEXT NOT NULL,
    "agent_id" TEXT NOT NULL,
    "avg_response_time" INTEGER NOT NULL DEFAULT 0,
    "avg_resolution_time" INTEGER NOT NULL DEFAULT 0,
    "completion_rate" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "quality_score" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "total_tasks_completed" INTEGER NOT NULL DEFAULT 0,
    "total_tasks_assigned" INTEGER NOT NULL DEFAULT 0,
    "last_updated" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "agent_performance_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "agent_skills_agent_id_skill_name_key" ON "agent_skills"("agent_id", "skill_name");

-- CreateIndex
CREATE UNIQUE INDEX "task_required_skills_task_id_skill_name_key" ON "task_required_skills"("task_id", "skill_name");

-- CreateIndex
CREATE UNIQUE INDEX "skills_catalog_organization_id_name_key" ON "skills_catalog"("organization_id", "name");

-- CreateIndex
CREATE INDEX "agent_availability_agent_id_status_idx" ON "agent_availability"("agent_id", "status");

-- CreateIndex
CREATE INDEX "agent_metrics_agent_id_period_idx" ON "agent_metrics"("agent_id", "period");

-- CreateIndex
CREATE UNIQUE INDEX "agent_metrics_agent_id_period_start_date_key" ON "agent_metrics"("agent_id", "period", "start_date");

-- CreateIndex
CREATE INDEX "escalations_task_id_status_idx" ON "escalations"("task_id", "status");

-- CreateIndex
CREATE INDEX "routing_rules_organization_id_priority_idx" ON "routing_rules"("organization_id", "priority");

-- CreateIndex
CREATE UNIQUE INDEX "routing_strategies_organization_id_name_key" ON "routing_strategies"("organization_id", "name");

-- CreateIndex
CREATE INDEX "rule_executions_task_id_idx" ON "rule_executions"("task_id");

-- CreateIndex
CREATE UNIQUE INDEX "sla_policies_organization_id_name_key" ON "sla_policies"("organization_id", "name");

-- CreateIndex
CREATE UNIQUE INDEX "task_metrics_task_id_key" ON "task_metrics"("task_id");

-- CreateIndex
CREATE INDEX "task_metrics_assigned_agent_id_idx" ON "task_metrics"("assigned_agent_id");

-- CreateIndex
CREATE INDEX "task_metrics_task_id_idx" ON "task_metrics"("task_id");

-- CreateIndex
CREATE INDEX "team_metrics_organization_id_period_idx" ON "team_metrics"("organization_id", "period");

-- CreateIndex
CREATE UNIQUE INDEX "team_metrics_organization_id_period_start_date_key" ON "team_metrics"("organization_id", "period", "start_date");

-- CreateIndex
CREATE UNIQUE INDEX "working_hours_agent_id_dayOfWeek_key" ON "working_hours"("agent_id", "dayOfWeek");

-- CreateIndex
CREATE UNIQUE INDEX "agent_performance_agent_id_key" ON "agent_performance"("agent_id");

-- CreateIndex
CREATE INDEX "tasks_sla_policy_id_idx" ON "tasks"("sla_policy_id");

-- AddForeignKey
ALTER TABLE "tasks" ADD CONSTRAINT "tasks_sla_policy_id_fkey" FOREIGN KEY ("sla_policy_id") REFERENCES "sla_policies"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "agent_skills" ADD CONSTRAINT "agent_skills_agent_id_fkey" FOREIGN KEY ("agent_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "task_required_skills" ADD CONSTRAINT "task_required_skills_task_id_fkey" FOREIGN KEY ("task_id") REFERENCES "tasks"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "skills_catalog" ADD CONSTRAINT "skills_catalog_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "agent_availability" ADD CONSTRAINT "agent_availability_agent_id_fkey" FOREIGN KEY ("agent_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "agent_metrics" ADD CONSTRAINT "agent_metrics_agent_id_fkey" FOREIGN KEY ("agent_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "escalations" ADD CONSTRAINT "escalations_task_id_fkey" FOREIGN KEY ("task_id") REFERENCES "tasks"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "routing_rules" ADD CONSTRAINT "routing_rules_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "routing_strategies" ADD CONSTRAINT "routing_strategies_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "rule_executions" ADD CONSTRAINT "rule_executions_task_id_fkey" FOREIGN KEY ("task_id") REFERENCES "tasks"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "sla_policies" ADD CONSTRAINT "sla_policies_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "task_metrics" ADD CONSTRAINT "task_metrics_assigned_agent_id_fkey" FOREIGN KEY ("assigned_agent_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "task_metrics" ADD CONSTRAINT "task_metrics_task_id_fkey" FOREIGN KEY ("task_id") REFERENCES "tasks"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "team_metrics" ADD CONSTRAINT "team_metrics_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "working_hours" ADD CONSTRAINT "working_hours_agent_id_fkey" FOREIGN KEY ("agent_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "agent_performance" ADD CONSTRAINT "agent_performance_agent_id_fkey" FOREIGN KEY ("agent_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
