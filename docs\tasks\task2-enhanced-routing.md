# Task 2: Enhanced Routing Engine

## 🎯 Objective

Enhance the existing task routing engine with intelligent algorithms that consider skills, workload, performance metrics, and multiple routing strategies for optimal task assignment.

## 📋 Status

- **Priority**: P1 (Critical)
- **Status**: ⏳ Waiting for Task 1
- **Estimated Time**: 6-8 hours
- **Dependencies**: Task 1 (Skills Management)

## 🔧 Technical Requirements

### Enhanced Routing Strategies

1. **Weighted Round Robin** (Current basic version needs enhancement)
2. **Best Skill Match** (New)
3. **Performance-Based** (New)
4. **Hybrid Intelligent** (New - combines multiple factors)

### Database Schema Updates

```prisma
model AgentPerformance {
  id                  String   @id @default(cuid())
  agentId             String   @unique @map("agent_id")
  avgResponseTime     Int      @default(0) @map("avg_response_time") // minutes
  avgResolutionTime   Int      @default(0) @map("avg_resolution_time") // minutes
  completionRate      Float    @default(0.0) @map("completion_rate") // 0.0-1.0
  qualityScore        Float    @default(0.0) @map("quality_score") // 0.0-100.0
  totalTasksCompleted Int      @default(0) @map("total_tasks_completed")
  totalTasksAssigned  Int      @default(0) @map("total_tasks_assigned")
  lastUpdated         DateTime @default(now()) @map("last_updated")
  
  agent User @relation(fields: [agentId], references: [id], onDelete: Cascade)
  
  @@map("agent_performance")
}

model RoutingStrategy {
  id             String   @id @default(cuid())
  organizationId String   @map("organization_id")
  name           String
  strategy       String   // 'round_robin', 'weighted_round_robin', 'best_match', 'performance_based', 'hybrid'
  isDefault      Boolean  @default(false) @map("is_default")
  isActive       Boolean  @default(true) @map("is_active")
  configuration  Json     @default("{}") // Strategy-specific config
  createdAt      DateTime @default(now()) @map("created_at")
  
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  @@unique([organizationId, name])
  @@map("routing_strategies")
}
```

### Enhanced Task Router Class

```typescript
export interface RoutingContext {
  task: TaskWithSkills;
  availableAgents: AgentWithSkills[];
  organizationId: string;
  strategy?: string;
  urgencyMultiplier?: number;
}

export interface RoutingResult {
  selectedAgent: AgentWithSkills;
  confidence: number; // 0-100
  reasoning: string[];
  alternativeAgents: AgentWithSkills[];
}

export class EnhancedTaskRouter {
  // Core routing method
  async routeTask(context: RoutingContext): Promise<RoutingResult>;
  
  // Strategy implementations
  async weightedRoundRobin(context: RoutingContext): Promise<RoutingResult>;
  async bestSkillMatch(context: RoutingContext): Promise<RoutingResult>;
  async performanceBased(context: RoutingContext): Promise<RoutingResult>;
  async hybridIntelligent(context: RoutingContext): Promise<RoutingResult>;
  
  // Utility methods
  calculateSkillMatchScore(agentSkills: AgentSkill[], requiredSkills: TaskRequiredSkill[]): number;
  calculateWorkloadScore(agent: AgentWithSkills): number;
  calculatePerformanceScore(agent: AgentWithSkills): number;
  calculateUrgencyWeight(task: TaskWithSkills): number;
}
```

## 🛠️ Implementation Steps

### Step 1: Database Schema Updates (45 minutes)

1. **Add Performance Tracking Model**
   - Update Prisma schema with AgentPerformance model
   - Add RoutingStrategy model for configurable strategies

2. **Create Migration**
   ```bash
   npx prisma migrate dev --name add-enhanced-routing
   ```

3. **Update Seed Data**
   - Add performance data for demo agents
   - Create default routing strategies

### Step 2: Enhanced Routing Engine (120 minutes)

1. **Create `lib/enhanced-task-router.ts`**
   ```typescript
   export class EnhancedTaskRouter {
     async routeTask(context: RoutingContext): Promise<RoutingResult> {
       const strategy = context.strategy || await this.getDefaultStrategy(context.organizationId);
       
       switch (strategy) {
         case 'weighted_round_robin':
           return this.weightedRoundRobin(context);
         case 'best_skill_match':
           return this.bestSkillMatch(context);
         case 'performance_based':
           return this.performanceBased(context);
         case 'hybrid_intelligent':
           return this.hybridIntelligent(context);
         default:
           return this.weightedRoundRobin(context);
       }
     }

     async weightedRoundRobin(context: RoutingContext): Promise<RoutingResult> {
       // Enhanced version with skill matching
       const { task, availableAgents } = context;
       
       const scoredAgents = availableAgents.map(agent => {
         const workloadScore = this.calculateWorkloadScore(agent);
         const skillScore = this.calculateSkillMatchScore(agent.skills, task.requiredSkills);
         const performanceScore = this.calculatePerformanceScore(agent);
         
         const totalScore = (workloadScore * 0.4) + (skillScore * 0.4) + (performanceScore * 0.2);
         
         return { agent, score: totalScore };
       });
       
       // Weighted random selection
       return this.selectByWeight(scoredAgents, task);
     }

     async bestSkillMatch(context: RoutingContext): Promise<RoutingResult> {
       // Prioritize skill matching above all else
       const { task, availableAgents } = context;
       
       const scoredAgents = availableAgents.map(agent => {
         const skillScore = this.calculateSkillMatchScore(agent.skills, task.requiredSkills);
         const workloadScore = this.calculateWorkloadScore(agent);
         
         const totalScore = (skillScore * 0.8) + (workloadScore * 0.2);
         
         return { agent, score: totalScore };
       });
       
       return this.selectBestScore(scoredAgents, task);
     }

     async performanceBased(context: RoutingContext): Promise<RoutingResult> {
       // Prioritize historical performance
       const { task, availableAgents } = context;
       
       const scoredAgents = availableAgents.map(agent => {
         const performanceScore = this.calculatePerformanceScore(agent);
         const skillScore = this.calculateSkillMatchScore(agent.skills, task.requiredSkills);
         const workloadScore = this.calculateWorkloadScore(agent);
         
         const totalScore = (performanceScore * 0.5) + (skillScore * 0.3) + (workloadScore * 0.2);
         
         return { agent, score: totalScore };
       });
       
       return this.selectBestScore(scoredAgents, task);
     }

     async hybridIntelligent(context: RoutingContext): Promise<RoutingResult> {
       // Dynamic weighting based on task characteristics
       const { task, availableAgents } = context;
       const urgencyWeight = this.calculateUrgencyWeight(task);
       
       const scoredAgents = availableAgents.map(agent => {
         const skillScore = this.calculateSkillMatchScore(agent.skills, task.requiredSkills);
         const workloadScore = this.calculateWorkloadScore(agent);
         const performanceScore = this.calculatePerformanceScore(agent);
         
         // Dynamic weighting based on urgency
         let skillWeight = 0.4;
         let performanceWeight = 0.3;
         let workloadWeight = 0.3;
         
         if (urgencyWeight > 0.8) {
           // High urgency: prioritize performance and availability
           performanceWeight = 0.5;
           workloadWeight = 0.4;
           skillWeight = 0.1;
         } else if (task.requiredSkills.length > 0) {
           // Skill-specific task: prioritize skill match
           skillWeight = 0.6;
           performanceWeight = 0.2;
           workloadWeight = 0.2;
         }
         
         const totalScore = (skillScore * skillWeight) + 
                           (performanceScore * performanceWeight) + 
                           (workloadScore * workloadWeight);
         
         return { agent, score: totalScore };
       });
       
       return this.selectByWeight(scoredAgents, task);
     }
   }
   ```

### Step 3: Scoring Algorithms (90 minutes)

1. **Skill Matching Algorithm**
   ```typescript
   calculateSkillMatchScore(agentSkills: AgentSkill[], requiredSkills: TaskRequiredSkill[]): number {
     if (requiredSkills.length === 0) return 1.0; // No requirements = perfect match
     
     let totalMatch = 0;
     let totalRequired = 0;
     
     for (const required of requiredSkills) {
       const agentSkill = agentSkills.find(s => s.skillName === required.skillName);
       
       if (agentSkill) {
         // Calculate match quality based on proficiency vs requirement
         const matchQuality = Math.min(agentSkill.proficiencyLevel / required.requiredLevel, 1.0);
         totalMatch += matchQuality;
       }
       totalRequired += 1;
     }
     
     return totalMatch / totalRequired;
   }
   ```

2. **Workload Scoring**
   ```typescript
   calculateWorkloadScore(agent: AgentWithSkills): number {
     const utilization = agent.currentTaskCount / agent.maxConcurrentTasks;
     return Math.max(0, 1 - utilization); // Higher score for lower utilization
   }
   ```

3. **Performance Scoring**
   ```typescript
   calculatePerformanceScore(agent: AgentWithSkills): number {
     if (!agent.performance) return 0.5; // Default score for new agents
     
     const completionScore = agent.performance.completionRate;
     const qualityScore = agent.performance.qualityScore / 100;
     const responseScore = Math.max(0, 1 - (agent.performance.avgResponseTime / 60)); // Normalize to hours
     
     return (completionScore * 0.4) + (qualityScore * 0.4) + (responseScore * 0.2);
   }
   ```

### Step 4: API Enhancements (60 minutes)

1. **Enhanced Assignment API** - `app/api/tasks/[id]/assign/route.ts`
   - Add strategy selection parameter
   - Return routing confidence and reasoning
   - Provide alternative agent suggestions

2. **Routing Strategies API** - `app/api/routing/strategies/route.ts`
   - GET: List available strategies
   - POST: Create custom strategy
   - PATCH: Update strategy configuration

3. **Performance Tracking API** - `app/api/agents/[id]/performance/route.ts`
   - GET: Get agent performance metrics
   - POST: Update performance data (for completed tasks)

### Step 5: Integration with Existing Router (45 minutes)

1. **Update `lib/task-router.ts`**
   - Integrate EnhancedTaskRouter
   - Maintain backward compatibility
   - Add performance tracking hooks

2. **Update Task Completion Logic**
   - Track completion times
   - Update agent performance metrics
   - Calculate quality scores

## 🧪 Testing Requirements

### Unit Tests
- Each routing strategy algorithm
- Scoring calculation methods
- Performance metric updates

### Integration Tests
- End-to-end task assignment with different strategies
- Performance tracking accuracy
- API endpoint functionality

### Performance Tests
- Routing speed with large agent pools
- Memory usage with complex scoring
- Database query optimization

## 📝 Acceptance Criteria

- [x] Multiple routing strategies are available and configurable
- [x] Skill matching influences task assignment appropriately
- [x] Performance metrics are tracked and used in routing decisions
- [x] Routing confidence and reasoning are provided
- [ ] Alternative agent suggestions are available
- [x] Backward compatibility with existing routing is maintained
- [x] Performance tracking updates automatically on task completion

## 🔗 Dependencies for Next Tasks

This task enables:
- **Task 3**: Working hours integration with enhanced routing
- **Task 4**: Routing rules that can specify strategies
- **Task 6**: Performance metrics dashboard

## 📋 Deliverables

1. Enhanced routing engine with multiple strategies
2. Performance tracking database schema
3. Skill matching algorithms
4. Updated API endpoints with strategy selection
5. Performance metrics calculation
6. Integration with existing task router
7. Comprehensive unit tests
8. Documentation for routing strategies

**Next Task**: [Task 3: Working Hours & Availability](./task3-working-hours.md)
