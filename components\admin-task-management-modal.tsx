"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { format } from "date-fns"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Edit3 } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { toast } from "sonner"
import { AgentSelector } from "./agent-selector"

interface Task {
  id: string
  title: string
  description?: string
  priority: string
  type: string
  status: string
  estimatedDuration?: number
  source: string
  assignedTo?: string
  assignedAt?: string
  createdAt: string
  updatedAt: string
  assignedUser?: {
    id: string
    name: string
    email: string
  }
}

interface AdminTaskManagementModalProps {
  task: Task
  onTaskUpdated?: () => void
  onModalOpen?: () => void
  onModalClose?: () => void
  trigger?: React.ReactNode
}

// Form validation schema
const taskUpdateSchema = z.object({
  title: z.string().min(1, "Title is required").max(100, "Title must be less than 100 characters"),
  description: z.string().optional(),
  priority: z.enum(["LOW", "MEDIUM", "HIGH", "URGENT"]),
  type: z.string().min(1, "Type is required"),
  estimatedDuration: z.number().min(1, "Duration must be at least 1 minute").optional(),
  status: z.enum(["PENDING", "ASSIGNED", "IN_PROGRESS", "COMPLETED", "ESCALATED"]),
  assignedTo: z.string().optional(),
})

type TaskUpdateValues = z.infer<typeof taskUpdateSchema>

export function AdminTaskManagementModal({
  task,
  onTaskUpdated,
  onModalOpen,
  onModalClose,
  trigger
}: AdminTaskManagementModalProps) {
  const { data: session } = useSession()
  const [open, setOpen] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isEditing, setIsEditing] = useState(false)

  // Only allow admin users to access this modal
  const isAdmin = session?.user?.role === "ADMIN"

  const form = useForm<TaskUpdateValues>({
    resolver: zodResolver(taskUpdateSchema),
    defaultValues: {
      title: task.title,
      description: task.description || "",
      priority: task.priority as "LOW" | "MEDIUM" | "HIGH" | "URGENT",
      type: task.type,
      estimatedDuration: task.estimatedDuration,
      status: task.status as "PENDING" | "ASSIGNED" | "IN_PROGRESS" | "COMPLETED" | "ESCALATED",
      assignedTo: task.assignedTo || undefined,
    },
  })

  // Reset form when task changes or modal opens
  useEffect(() => {
    if (open) {
      form.reset({
        title: task.title,
        description: task.description || "",
        priority: task.priority as "LOW" | "MEDIUM" | "HIGH" | "URGENT",
        type: task.type,
        estimatedDuration: task.estimatedDuration,
        status: task.status as "PENDING" | "ASSIGNED" | "IN_PROGRESS" | "COMPLETED" | "ESCALATED",
        assignedTo: task.assignedTo || undefined,
      })
      setIsEditing(false)
      // Notify parent that modal is open
      if (onModalOpen) {
        onModalOpen()
      }
    } else {
      // Notify parent that modal is closed
      if (onModalClose) {
        onModalClose()
      }
    }
  }, [task, open, form, onModalOpen, onModalClose])

  const onSubmit = async (values: TaskUpdateValues) => {
    setIsSubmitting(true)
    
    try {
      const response = await fetch(`/api/tasks/${task.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...values,
          estimatedDuration: values.estimatedDuration ? Number(values.estimatedDuration) : undefined,
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error?.message || "Failed to update task")
      }

      // Success
      toast.success("Task updated successfully")
      setOpen(false)
      setIsEditing(false)

      // Callback for parent component
      if (onTaskUpdated) {
        onTaskUpdated()
      }
    } catch (error) {
      console.error("Error updating task:", error)
      toast.error(error instanceof Error ? error.message : "Failed to update task")
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleAssignmentChange = async (agentId: string | undefined) => {
    if (agentId === task.assignedTo) return // No change

    setIsSubmitting(true)
    
    try {
      if (agentId) {
        // Assign to specific agent
        const response = await fetch(`/api/tasks/${task.id}/assign`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ agentId }),
        })

        const result = await response.json()

        if (!response.ok) {
          throw new Error(result.error?.message || "Failed to assign task")
        }

        toast.success(`Task assigned successfully`)
      } else {
        // Unassign task
        const response = await fetch(`/api/tasks/${task.id}`, {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ 
            assignedTo: null,
            status: "PENDING"
          }),
        })

        const result = await response.json()

        if (!response.ok) {
          throw new Error(result.error?.message || "Failed to unassign task")
        }

        toast.success("Task unassigned successfully")
      }

      // Update form value
      form.setValue("assignedTo", agentId)
      
      // Callback for parent component
      if (onTaskUpdated) {
        onTaskUpdated()
      }
    } catch (error) {
      console.error("Error updating assignment:", error)
      toast.error(error instanceof Error ? error.message : "Failed to update assignment")
    } finally {
      setIsSubmitting(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "PENDING":
        return "bg-yellow-500"
      case "ASSIGNED":
        return "bg-blue-500"
      case "IN_PROGRESS":
        return "bg-purple-500"
      case "COMPLETED":
        return "bg-green-500"
      case "ESCALATED":
        return "bg-red-500"
      default:
        return "bg-gray-500"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "LOW":
        return "bg-green-100 text-green-800"
      case "MEDIUM":
        return "bg-yellow-100 text-yellow-800"
      case "HIGH":
        return "bg-orange-100 text-orange-800"
      case "URGENT":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  // Don't render the modal for non-admin users
  if (!isAdmin) {
    return trigger || null
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || <Button variant="outline">Manage Task</Button>}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            Task Management
            <Badge className={getStatusColor(task.status) + " text-white"}>
              {task.status.replace('_', ' ')}
            </Badge>
          </DialogTitle>
          <DialogDescription>
            View and manage task details, assignment, and status
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Task Info Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Task Information</h3>
              <div className="flex items-center gap-2">
                <Badge className={getPriorityColor(task.priority)}>
                  {task.priority}
                </Badge>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsEditing(!isEditing)}
                  disabled={isSubmitting || !isAdmin}
                >
                  <Edit3 className="h-4 w-4 mr-1" />
                  {isEditing ? "Cancel" : "Edit"}
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Created:</span>
                <div className="text-muted-foreground">
                  {format(new Date(task.createdAt), 'MMM dd, yyyy HH:mm')}
                </div>
              </div>
              <div>
                <span className="font-medium">Last Updated:</span>
                <div className="text-muted-foreground">
                  {format(new Date(task.updatedAt), 'MMM dd, yyyy HH:mm')}
                </div>
              </div>
              <div>
                <span className="font-medium">Source:</span>
                <div className="text-muted-foreground">{task.source}</div>
              </div>
              <div>
                <span className="font-medium">Type:</span>
                <div className="text-muted-foreground">{task.type}</div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Assignment Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <UserCheck className="h-5 w-5" />
              <h3 className="text-lg font-semibold">Assignment</h3>
            </div>
            
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium">Assigned Agent</label>
                <div className="mt-1">
                  <AgentSelector
                    value={form.watch("assignedTo")}
                    onValueChange={handleAssignmentChange}
                    disabled={isSubmitting || !isAdmin}
                    placeholder="Select an agent..."
                    taskId={task.id}
                    showAutoAssign={isAdmin}
                  />
                </div>
              </div>
              
              {task.assignedUser && (
                <div className="text-sm text-muted-foreground">
                  Currently assigned to: <span className="font-medium">{task.assignedUser.name}</span>
                  {task.assignedAt && (
                    <span> on {format(new Date(task.assignedAt), 'MMM dd, yyyy HH:mm')}</span>
                  )}
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Edit Form Section */}
          {isEditing && isAdmin && (
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <div className="flex items-center gap-2">
                  <Edit3 className="h-5 w-5" />
                  <h3 className="text-lg font-semibold">Edit Task Details</h3>
                </div>

                {/* Title */}
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Title *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter task title..." {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Description */}
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Enter task description..."
                          className="min-h-[100px]"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  {/* Priority */}
                  <FormField
                    control={form.control}
                    name="priority"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Priority *</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select priority" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="LOW">Low</SelectItem>
                            <SelectItem value="MEDIUM">Medium</SelectItem>
                            <SelectItem value="HIGH">High</SelectItem>
                            <SelectItem value="URGENT">Urgent</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Status */}
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Status *</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="PENDING">Pending</SelectItem>
                            <SelectItem value="ASSIGNED">Assigned</SelectItem>
                            <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                            <SelectItem value="COMPLETED">Completed</SelectItem>
                            <SelectItem value="ESCALATED">Escalated</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  {/* Type */}
                  <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Type *</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., support, sales, review" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Estimated Duration */}
                  <FormField
                    control={form.control}
                    name="estimatedDuration"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Estimated Duration (minutes)</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            placeholder="e.g., 30"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsEditing(false)}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isSubmitting}>
                    <Save className="h-4 w-4 mr-2" />
                    {isSubmitting ? "Saving..." : "Save Changes"}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
