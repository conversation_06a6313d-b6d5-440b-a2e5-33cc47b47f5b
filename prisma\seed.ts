import {
  PrismaClient,
  UserStatus,
  UserRole,
  TaskStatus,
  TaskPriority,
  TaskEventType,
} from "@prisma/client";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

async function main() {
  console.log("🌱 Starting database seed...");

  // Create demo organization
  const organization = await prisma.organization.upsert({
    where: { id: "demo-org" },
    update: {},
    create: {
      id: "demo-org",
      name: "Demo Organization",
    },
  });

  console.log("✅ Created organization:", organization.name);

  // Create demo users (admin and agents)
  console.log("👥 Creating demo users...");

  // Hash passwords for demo users
  const defaultPassword = await bcrypt.hash("demo123", 12);

  const users = await Promise.all([
    // Admin user
    prisma.user.upsert({
      where: { email: "<EMAIL>" },
      update: {},
      create: {
        name: "Admin User",
        email: "<EMAIL>",
        password: defaultPassword,
        role: UserRole.ADMIN,
        organizationId: organization.id,
        maxConcurrentTasks: 10,
        currentTaskCount: 0,
        status: UserStatus.AVAILABLE,
      },
    }),
    // Agent users
    prisma.user.upsert({
      where: { email: "<EMAIL>" },
      update: {},
      create: {
        name: "Alice Johnson",
        email: "<EMAIL>",
        password: defaultPassword,
        role: UserRole.AGENT,
        organizationId: organization.id,
        maxConcurrentTasks: 5,
        currentTaskCount: 2,
        status: UserStatus.AVAILABLE,
      },
    }),
    prisma.user.upsert({
      where: { email: "<EMAIL>" },
      update: {},
      create: {
        name: "Bob Smith",
        email: "<EMAIL>",
        password: defaultPassword,
        role: UserRole.AGENT,
        organizationId: organization.id,
        maxConcurrentTasks: 3,
        currentTaskCount: 1,
        status: UserStatus.AVAILABLE,
      },
    }),
    prisma.user.upsert({
      where: { email: "<EMAIL>" },
      update: {},
      create: {
        name: "Charlie Brown",
        email: "<EMAIL>",
        password: defaultPassword,
        role: UserRole.AGENT,
        organizationId: organization.id,
        maxConcurrentTasks: 4,
        currentTaskCount: 0,
        status: UserStatus.AWAY,
      },
    }),
  ]);

  console.log("✅ Created users:", users.map((u) => u.name).join(", "));

  // Create demo tasks
  const tasks = await Promise.all([
    prisma.task.create({
      data: {
        title: "Customer Support Ticket #1001",
        description: "Customer having trouble with login functionality",
        priority: TaskPriority.HIGH,
        type: "support",
        estimatedDuration: 30,
        source: "email",
        organizationId: organization.id,
        assignedTo: users[1].id, // Alice (Customer Support specialist)
        assignedAt: new Date(),
        status: TaskStatus.IN_PROGRESS,
        response_by: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours from now
        resolve_by: new Date(Date.now() + 8 * 60 * 60 * 1000), // 8 hours from now
      },
    }),
    prisma.task.create({
      data: {
        title: "Sales Lead Follow-up",
        description: "Follow up with potential enterprise client",
        priority: TaskPriority.MEDIUM,
        type: "sales",
        estimatedDuration: 45,
        source: "crm",
        organizationId: organization.id,
        assignedTo: users[3].id, // Charlie (Sales specialist)
        assignedAt: new Date(),
        status: TaskStatus.ASSIGNED,
        response_by: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
        resolve_by: new Date(Date.now() + 72 * 60 * 60 * 1000), // 72 hours from now
      },
    }),
    prisma.task.create({
      data: {
        title: "Code Review Request",
        description: "Review pull request for new feature implementation",
        priority: TaskPriority.MEDIUM,
        type: "review",
        estimatedDuration: 60,
        source: "github",
        organizationId: organization.id,
        status: TaskStatus.PENDING,
        response_by: new Date(Date.now() + 4 * 60 * 60 * 1000), // 4 hours from now
        resolve_by: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
      },
    }),
    prisma.task.create({
      data: {
        title: "Urgent Bug Fix",
        description: "Critical bug affecting payment processing",
        priority: TaskPriority.URGENT,
        type: "bug",
        estimatedDuration: 120,
        source: "monitoring",
        organizationId: organization.id,
        assignedTo: users[2].id, // Bob (Technical specialist)
        assignedAt: new Date(),
        status: TaskStatus.IN_PROGRESS,
        response_by: new Date(Date.now() + 1 * 60 * 60 * 1000), // 1 hour from now
        resolve_by: new Date(Date.now() + 4 * 60 * 60 * 1000), // 4 hours from now
      },
    }),
  ]);

  console.log("✅ Created tasks:", tasks.map((t) => t.title).join(", "));

  // Create task events for audit trail
  for (const task of tasks) {
    await prisma.taskEvent.create({
      data: {
        taskId: task.id,
        type: TaskEventType.CREATED,
        data: {
          title: task.title,
          priority: task.priority,
          type: task.type,
        },
      },
    });

    if (task.assignedTo) {
      await prisma.taskEvent.create({
        data: {
          taskId: task.id,
          userId: task.assignedTo,
          type: TaskEventType.ASSIGNED,
          data: {
            assignedTo: task.assignedTo,
            assignedAt: task.assignedAt,
          },
        },
      });
    }
  }

  console.log("✅ Created task events");

  // Create skills catalog
  console.log("🎯 Creating skills catalog...");

  const skills = await Promise.all([
    prisma.skillsCatalog.upsert({
      where: { organizationId_name: { organizationId: organization.id, name: "JavaScript" } },
      update: {},
      create: {
        organizationId: organization.id,
        name: "JavaScript",
        description: "JavaScript programming language",
        category: "technical",
      },
    }),
    prisma.skillsCatalog.upsert({
      where: { organizationId_name: { organizationId: organization.id, name: "Customer Support" } },
      update: {},
      create: {
        organizationId: organization.id,
        name: "Customer Support",
        description: "Customer service and support skills",
        category: "domain",
      },
    }),
    prisma.skillsCatalog.upsert({
      where: { organizationId_name: { organizationId: organization.id, name: "Sales" } },
      update: {},
      create: {
        organizationId: organization.id,
        name: "Sales",
        description: "Sales and business development",
        category: "domain",
      },
    }),
    prisma.skillsCatalog.upsert({
      where: { organizationId_name: { organizationId: organization.id, name: "Code Review" } },
      update: {},
      create: {
        organizationId: organization.id,
        name: "Code Review",
        description: "Code review and quality assurance",
        category: "technical",
      },
    }),
    prisma.skillsCatalog.upsert({
      where: { organizationId_name: { organizationId: organization.id, name: "Bug Fixing" } },
      update: {},
      create: {
        organizationId: organization.id,
        name: "Bug Fixing",
        description: "Debugging and issue resolution",
        category: "technical",
      },
    }),
    prisma.skillsCatalog.upsert({
      where: { organizationId_name: { organizationId: organization.id, name: "Spanish" } },
      update: {},
      create: {
        organizationId: organization.id,
        name: "Spanish",
        description: "Spanish language proficiency",
        category: "language",
      },
    }),
  ]);

  console.log("✅ Created skills:", skills.map((s) => s.name).join(", "));

  // Assign skills to agents
  console.log("👨‍💼 Assigning skills to agents...");

  const [admin, alice, bob, charlie] = users;

  // Alice's skills (Customer Support specialist)
  await Promise.all([
    prisma.agentSkill.upsert({
      where: { agentId_skillName: { agentId: alice.id, skillName: "Customer Support" } },
      update: {},
      create: { agentId: alice.id, skillName: "Customer Support", proficiencyLevel: 5 },
    }),
    prisma.agentSkill.upsert({
      where: { agentId_skillName: { agentId: alice.id, skillName: "Spanish" } },
      update: {},
      create: { agentId: alice.id, skillName: "Spanish", proficiencyLevel: 4 },
    }),
    prisma.agentSkill.upsert({
      where: { agentId_skillName: { agentId: alice.id, skillName: "JavaScript" } },
      update: {},
      create: { agentId: alice.id, skillName: "JavaScript", proficiencyLevel: 2 },
    }),
  ]);

  // Bob's skills (Technical specialist)
  await Promise.all([
    prisma.agentSkill.upsert({
      where: { agentId_skillName: { agentId: bob.id, skillName: "JavaScript" } },
      update: {},
      create: { agentId: bob.id, skillName: "JavaScript", proficiencyLevel: 5 },
    }),
    prisma.agentSkill.upsert({
      where: { agentId_skillName: { agentId: bob.id, skillName: "Code Review" } },
      update: {},
      create: { agentId: bob.id, skillName: "Code Review", proficiencyLevel: 4 },
    }),
    prisma.agentSkill.upsert({
      where: { agentId_skillName: { agentId: bob.id, skillName: "Bug Fixing" } },
      update: {},
      create: { agentId: bob.id, skillName: "Bug Fixing", proficiencyLevel: 5 },
    }),
  ]);

  // Charlie's skills (Sales and support)
  await Promise.all([
    prisma.agentSkill.upsert({
      where: { agentId_skillName: { agentId: charlie.id, skillName: "Sales" } },
      update: {},
      create: { agentId: charlie.id, skillName: "Sales", proficiencyLevel: 4 },
    }),
    prisma.agentSkill.upsert({
      where: { agentId_skillName: { agentId: charlie.id, skillName: "Customer Support" } },
      update: {},
      create: { agentId: charlie.id, skillName: "Customer Support", proficiencyLevel: 3 },
    }),
  ]);

  console.log("✅ Assigned skills to agents");

  // Add skill requirements to some tasks
  console.log("📋 Adding skill requirements to tasks...");

  const [supportTask, salesTask, reviewTask, bugTask] = tasks;

  // Customer Support task requires Customer Support skill
  await prisma.taskRequiredSkill.upsert({
    where: { taskId_skillName: { taskId: supportTask.id, skillName: "Customer Support" } },
    update: {},
    create: { taskId: supportTask.id, skillName: "Customer Support", requiredLevel: 3 },
  });

  // Sales task requires Sales skill
  await prisma.taskRequiredSkill.upsert({
    where: { taskId_skillName: { taskId: salesTask.id, skillName: "Sales" } },
    update: {},
    create: { taskId: salesTask.id, skillName: "Sales", requiredLevel: 3 },
  });

  // Code review task requires Code Review skill
  await prisma.taskRequiredSkill.upsert({
    where: { taskId_skillName: { taskId: reviewTask.id, skillName: "Code Review" } },
    update: {},
    create: { taskId: reviewTask.id, skillName: "Code Review", requiredLevel: 3 },
  });

  // Bug fix task requires JavaScript and Bug Fixing skills
  await Promise.all([
    prisma.taskRequiredSkill.upsert({
      where: { taskId_skillName: { taskId: bugTask.id, skillName: "JavaScript" } },
      update: {},
      create: { taskId: bugTask.id, skillName: "JavaScript", requiredLevel: 4 },
    }),
    prisma.taskRequiredSkill.upsert({
      where: { taskId_skillName: { taskId: bugTask.id, skillName: "Bug Fixing" } },
      update: {},
      create: { taskId: bugTask.id, skillName: "Bug Fixing", requiredLevel: 4 },
    }),
  ]);

  console.log("✅ Added skill requirements to tasks");

  // Create performance metrics for agents
  console.log("📊 Creating performance metrics for agents...");

  await Promise.all([
    // Alice's performance (Customer Support specialist)
    prisma.agentPerformance.upsert({
      where: { agentId: alice.id },
      update: {},
      create: {
        agentId: alice.id,
        avgResponseTime: 15, // 15 minutes average response
        avgResolutionTime: 120, // 2 hours average resolution
        completionRate: 0.95, // 95% completion rate
        qualityScore: 88.5, // 88.5% quality score
        totalTasksCompleted: 47,
        totalTasksAssigned: 50,
      },
    }),
    // Bob's performance (Technical specialist)
    prisma.agentPerformance.upsert({
      where: { agentId: bob.id },
      update: {},
      create: {
        agentId: bob.id,
        avgResponseTime: 25, // 25 minutes average response
        avgResolutionTime: 180, // 3 hours average resolution
        completionRate: 0.92, // 92% completion rate
        qualityScore: 92.0, // 92% quality score
        totalTasksCompleted: 34,
        totalTasksAssigned: 37,
      },
    }),
    // Charlie's performance (Sales specialist)
    prisma.agentPerformance.upsert({
      where: { agentId: charlie.id },
      update: {},
      create: {
        agentId: charlie.id,
        avgResponseTime: 45, // 45 minutes average response
        avgResolutionTime: 240, // 4 hours average resolution
        completionRate: 0.88, // 88% completion rate
        qualityScore: 85.0, // 85% quality score
        totalTasksCompleted: 28,
        totalTasksAssigned: 32,
      },
    }),
  ]);

  console.log("✅ Created performance metrics for agents");

  // Create default routing strategies
  console.log("🎯 Creating default routing strategies...");

  await Promise.all([
    prisma.routing_strategies.upsert({
      where: { organization_id_name: { organization_id: organization.id, name: "Weighted Round Robin" } },
      update: {},
      create: {
        id: "strategy-weighted-rr",
        organization_id: organization.id,
        name: "Weighted Round Robin",
        strategy: "weighted_round_robin",
        is_default: true,
        is_active: true,
        configuration: {
          description: "Balances workload, skills, and performance with equal weighting",
          weights: {
            workload: 0.4,
            skills: 0.4,
            performance: 0.2
          }
        },
      },
    }),
    prisma.routing_strategies.upsert({
      where: { organization_id_name: { organization_id: organization.id, name: "Best Skill Match" } },
      update: {},
      create: {
        id: "strategy-best-skill",
        organization_id: organization.id,
        name: "Best Skill Match",
        strategy: "best_skill_match",
        is_default: false,
        is_active: true,
        configuration: {
          description: "Prioritizes agents with the best skill match for the task",
          weights: {
            skills: 0.8,
            workload: 0.2
          }
        },
      },
    }),
    prisma.routing_strategies.upsert({
      where: { organization_id_name: { organization_id: organization.id, name: "Performance Based" } },
      update: {},
      create: {
        id: "strategy-performance",
        organization_id: organization.id,
        name: "Performance Based",
        strategy: "performance_based",
        is_default: false,
        is_active: true,
        configuration: {
          description: "Prioritizes agents with the best historical performance",
          weights: {
            performance: 0.5,
            skills: 0.3,
            workload: 0.2
          }
        },
      },
    }),
    prisma.routing_strategies.upsert({
      where: { organization_id_name: { organization_id: organization.id, name: "Hybrid Intelligent" } },
      update: {},
      create: {
        id: "strategy-hybrid",
        organization_id: organization.id,
        name: "Hybrid Intelligent",
        strategy: "hybrid_intelligent",
        is_default: false,
        is_active: true,
        configuration: {
          description: "Dynamically adjusts weighting based on task characteristics",
          adaptive: true,
          urgencyThreshold: 0.8
        },
      },
    }),
  ]);

  console.log("✅ Created default routing strategies");

  // Create organization settings
  console.log("⚙️ Creating organization settings...");

  await prisma.organizationSettings.upsert({
    where: { organizationId: organization.id },
    update: {},
    create: {
      organizationId: organization.id,
      defaultTimezone: "UTC",
      businessHoursStart: "09:00",
      businessHoursEnd: "17:00",
      businessDays: [1, 2, 3, 4, 5], // Monday-Friday
      afterHoursRouting: false,
      weekendRouting: false,
      urgentTasksOverride: true,
      overrideAgentHours: false,
    },
  });

  console.log("✅ Created organization settings");

  // Create working hours for agents
  console.log("🕒 Creating working hours for agents...");

  // Alice's working hours (Customer Support - 9-5 EST)
  await Promise.all([
    prisma.agentWorkingHours.upsert({
      where: { agentId_dayOfWeek: { agentId: alice.id, dayOfWeek: 1 } }, // Monday
      update: {},
      create: {
        agentId: alice.id,
        dayOfWeek: 1,
        startTime: "09:00",
        endTime: "17:00",
        timezone: "America/New_York",
        isActive: true,
      },
    }),
    prisma.agentWorkingHours.upsert({
      where: { agentId_dayOfWeek: { agentId: alice.id, dayOfWeek: 2 } }, // Tuesday
      update: {},
      create: {
        agentId: alice.id,
        dayOfWeek: 2,
        startTime: "09:00",
        endTime: "17:00",
        timezone: "America/New_York",
        isActive: true,
      },
    }),
    prisma.agentWorkingHours.upsert({
      where: { agentId_dayOfWeek: { agentId: alice.id, dayOfWeek: 3 } }, // Wednesday
      update: {},
      create: {
        agentId: alice.id,
        dayOfWeek: 3,
        startTime: "09:00",
        endTime: "17:00",
        timezone: "America/New_York",
        isActive: true,
      },
    }),
    prisma.agentWorkingHours.upsert({
      where: { agentId_dayOfWeek: { agentId: alice.id, dayOfWeek: 4 } }, // Thursday
      update: {},
      create: {
        agentId: alice.id,
        dayOfWeek: 4,
        startTime: "09:00",
        endTime: "17:00",
        timezone: "America/New_York",
        isActive: true,
      },
    }),
    prisma.agentWorkingHours.upsert({
      where: { agentId_dayOfWeek: { agentId: alice.id, dayOfWeek: 5 } }, // Friday
      update: {},
      create: {
        agentId: alice.id,
        dayOfWeek: 5,
        startTime: "09:00",
        endTime: "17:00",
        timezone: "America/New_York",
        isActive: true,
      },
    }),
  ]);

  // Bob's working hours (Technical - 10-6 PST)
  await Promise.all([
    prisma.agentWorkingHours.upsert({
      where: { agentId_dayOfWeek: { agentId: bob.id, dayOfWeek: 1 } },
      update: {},
      create: {
        agentId: bob.id,
        dayOfWeek: 1,
        startTime: "10:00",
        endTime: "18:00",
        timezone: "America/Los_Angeles",
        isActive: true,
      },
    }),
    prisma.agentWorkingHours.upsert({
      where: { agentId_dayOfWeek: { agentId: bob.id, dayOfWeek: 2 } },
      update: {},
      create: {
        agentId: bob.id,
        dayOfWeek: 2,
        startTime: "10:00",
        endTime: "18:00",
        timezone: "America/Los_Angeles",
        isActive: true,
      },
    }),
    prisma.agentWorkingHours.upsert({
      where: { agentId_dayOfWeek: { agentId: bob.id, dayOfWeek: 3 } },
      update: {},
      create: {
        agentId: bob.id,
        dayOfWeek: 3,
        startTime: "10:00",
        endTime: "18:00",
        timezone: "America/Los_Angeles",
        isActive: true,
      },
    }),
    prisma.agentWorkingHours.upsert({
      where: { agentId_dayOfWeek: { agentId: bob.id, dayOfWeek: 4 } },
      update: {},
      create: {
        agentId: bob.id,
        dayOfWeek: 4,
        startTime: "10:00",
        endTime: "18:00",
        timezone: "America/Los_Angeles",
        isActive: true,
      },
    }),
    prisma.agentWorkingHours.upsert({
      where: { agentId_dayOfWeek: { agentId: bob.id, dayOfWeek: 5 } },
      update: {},
      create: {
        agentId: bob.id,
        dayOfWeek: 5,
        startTime: "10:00",
        endTime: "18:00",
        timezone: "America/Los_Angeles",
        isActive: true,
      },
    }),
  ]);

  console.log("✅ Created working hours for agents");

  // Create availability status for agents
  console.log("📊 Creating availability status for agents...");

  // Delete existing availability records first
  await prisma.agentAvailability.deleteMany({
    where: {
      agentId: { in: [alice.id, bob.id, charlie.id] }
    }
  });

  await Promise.all([
    // Alice is available
    prisma.agentAvailability.create({
      data: {
        agentId: alice.id,
        status: "available",
        isTemporary: false,
      },
    }),
    // Bob is available
    prisma.agentAvailability.create({
      data: {
        agentId: bob.id,
        status: "available",
        isTemporary: false,
      },
    }),
    // Charlie is temporarily away
    prisma.agentAvailability.create({
      data: {
        agentId: charlie.id,
        status: "away",
        reason: "In a meeting",
        startTime: new Date(),
        endTime: new Date(Date.now() + 60 * 60 * 1000), // 1 hour from now
        isTemporary: true,
      },
    }),
  ]);

  console.log("✅ Created availability status for agents");
  console.log("🎉 Database seed completed successfully!");
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error("❌ Seed failed:", e);
    await prisma.$disconnect();
    process.exit(1);
  });
