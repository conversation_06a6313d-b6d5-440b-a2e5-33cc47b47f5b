import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { UpdateUserRequest } from '@/lib/types'

// GET /api/agents/[id] - Get agent details
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const agent = await prisma.user.findUnique({
      where: { id: params.id },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
        skills: true,
        assignedTasks: {
          select: {
            id: true,
            title: true,
            status: true,
            priority: true,
            type: true,
            createdAt: true,
            assignedAt: true,
          },
          orderBy: {
            assignedAt: 'desc',
          },
        },
        _count: {
          select: {
            assignedTasks: true,
          },
        },
      },
    })

    if (!agent) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'AGENT_NOT_FOUND',
            message: 'Agent not found',
          },
        },
        { status: 404 }
      )
    }

    // Calculate task statistics
    const taskStats = await prisma.task.groupBy({
      by: ['status'],
      where: {
        assignedTo: agent.id,
      },
      _count: {
        status: true,
      },
    })

    const stats = {
      pending: 0,
      assigned: 0,
      in_progress: 0,
      completed: 0,
      escalated: 0,
    }

    taskStats.forEach((stat) => {
      stats[stat.status.toLowerCase() as keyof typeof stats] = stat._count.status
    })

    const agentWithMetrics = {
      ...agent,
      taskStats: stats,
      utilizationRate: agent.maxConcurrentTasks > 0 
        ? (agent.currentTaskCount / agent.maxConcurrentTasks) * 100 
        : 0,
    }

    return NextResponse.json({
      success: true,
      data: agentWithMetrics,
    })
  } catch (error) {
    console.error('Error fetching agent:', error)
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'FETCH_AGENT_FAILED',
          message: 'Failed to fetch agent',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      },
      { status: 500 }
    )
  }
}

// PATCH /api/agents/[id] - Update agent
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body: UpdateUserRequest = await request.json()

    // Check if agent exists
    const existingAgent = await prisma.user.findUnique({
      where: { id: params.id },
    })

    if (!existingAgent) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'AGENT_NOT_FOUND',
            message: 'Agent not found',
          },
        },
        { status: 404 }
      )
    }

    // Check if email is being updated and if it already exists
    if (body.email && body.email !== existingAgent.email) {
      const emailExists = await prisma.user.findUnique({
        where: { email: body.email },
      })

      if (emailExists) {
        return NextResponse.json(
          {
            success: false,
            error: {
              code: 'EMAIL_EXISTS',
              message: 'User with this email already exists',
            },
          },
          { status: 409 }
        )
      }
    }

    // Prepare update data
    const updateData: Partial<UpdateUserRequest> = {}

    if (body.name !== undefined) {
      updateData.name = body.name
    }

    if (body.email !== undefined) {
      updateData.email = body.email
    }

    if (body.maxConcurrentTasks !== undefined) {
      updateData.maxConcurrentTasks = body.maxConcurrentTasks
    }

    if (body.status !== undefined) {
      updateData.status = body.status
    }

    // Update agent
    const updatedAgent = await prisma.user.update({
      where: { id: params.id },
      data: updateData,
      include: {
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            assignedTasks: true,
          },
        },
      },
    })

    return NextResponse.json({
      success: true,
      data: updatedAgent,
      message: 'Agent updated successfully',
    })

  } catch (error) {
    console.error('Error updating agent:', error)
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'UPDATE_AGENT_FAILED',
          message: 'Failed to update agent',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      },
      { status: 500 }
    )
  }
}

// DELETE /api/agents/[id] - Delete agent
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if agent exists
    const existingAgent = await prisma.user.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            assignedTasks: true,
          },
        },
      },
    })

    if (!existingAgent) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'AGENT_NOT_FOUND',
            message: 'Agent not found',
          },
        },
        { status: 404 }
      )
    }

    // Check if agent has assigned tasks
    if (existingAgent._count.assignedTasks > 0) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'AGENT_HAS_TASKS',
            message: 'Cannot delete agent with assigned tasks. Please reassign tasks first.',
          },
        },
        { status: 409 }
      )
    }

    // Delete agent
    await prisma.user.delete({
      where: { id: params.id },
    })

    return NextResponse.json({
      success: true,
      message: 'Agent deleted successfully',
    })

  } catch (error) {
    console.error('Error deleting agent:', error)
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'DELETE_AGENT_FAILED',
          message: 'Failed to delete agent',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      },
      { status: 500 }
    )
  }
}
