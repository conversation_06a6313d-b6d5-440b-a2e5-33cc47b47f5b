"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Settings, Save, RefreshCw } from "lucide-react";
import { toast } from "sonner";
import { OrganizationWorkingSettings } from "@/lib/types";

interface OrganizationSettingsProps {
  organizationId: string;
}

const TIMEZONES = [
  { value: "UTC", label: "UTC" },
  { value: "America/New_York", label: "Eastern Time (ET)" },
  { value: "America/Chicago", label: "Central Time (CT)" },
  { value: "America/Denver", label: "Mountain Time (MT)" },
  { value: "America/Los_Angeles", label: "Pacific Time (PT)" },
  { value: "Europe/London", label: "London (GMT)" },
  { value: "Europe/Paris", label: "Paris (CET)" },
  { value: "Asia/Tokyo", label: "Tokyo (JST)" },
];

const DAYS_OF_WEEK = [
  { value: 0, label: "Sunday" },
  { value: 1, label: "Monday" },
  { value: 2, label: "Tuesday" },
  { value: 3, label: "Wednesday" },
  { value: 4, label: "Thursday" },
  { value: 5, label: "Friday" },
  { value: 6, label: "Saturday" },
];

export function OrganizationSettings({ organizationId }: OrganizationSettingsProps) {
  const [settings, setSettings] = useState<OrganizationWorkingSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Fetch organization settings
  const fetchSettings = useCallback(async () => {
    try {
      const response = await fetch(`/api/organizations/${organizationId}/settings`);
      const result = await response.json();

      if (result.success) {
        setSettings(result.data);
      } else {
        throw new Error(result.error || "Failed to fetch settings");
      }
    } catch (error) {
      console.error("Error fetching organization settings:", error);
      toast.error("Failed to load organization settings");
    } finally {
      setLoading(false);
    }
  }, [organizationId]);

  // Save organization settings
  const saveSettings = async () => {
    if (!settings) return;

    setSaving(true);
    try {
      const response = await fetch(`/api/organizations/${organizationId}/settings`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(settings),
      });

      const result = await response.json();

      if (result.success) {
        toast.success("Organization settings saved successfully");
        setSettings(result.data);
      } else {
        throw new Error(result.error || "Failed to save settings");
      }
    } catch (error) {
      console.error("Error saving organization settings:", error);
      toast.error("Failed to save organization settings");
    } finally {
      setSaving(false);
    }
  };

  // Update settings
  const updateSetting = (field: keyof OrganizationWorkingSettings, value: string | boolean | number[]) => {
    if (!settings) return;
    setSettings({ ...settings, [field]: value });
  };

  // Toggle business day
  const toggleBusinessDay = (dayOfWeek: number) => {
    if (!settings) return;
    const businessDays = [...settings.businessDays];
    const index = businessDays.indexOf(dayOfWeek);
    
    if (index > -1) {
      businessDays.splice(index, 1);
    } else {
      businessDays.push(dayOfWeek);
      businessDays.sort();
    }
    
    updateSetting('businessDays', businessDays);
  };

  useEffect(() => {
    fetchSettings();
  }, [organizationId, fetchSettings]);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Organization Settings
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!settings) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Organization Settings
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            Failed to load organization settings
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Business Hours Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Business Hours Configuration
          </CardTitle>
          <CardDescription>
            Configure organization-wide business hours and routing policies
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Default Timezone */}
          <div className="space-y-2">
            <Label>Default Timezone</Label>
            <Select
              value={settings.defaultTimezone}
              onValueChange={(value) => updateSetting('defaultTimezone', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {TIMEZONES.map((tz) => (
                  <SelectItem key={tz.value} value={tz.value}>
                    {tz.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Business Hours */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Business Hours Start</Label>
              <Input
                type="time"
                value={settings.businessHoursStart}
                onChange={(e) => updateSetting('businessHoursStart', e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label>Business Hours End</Label>
              <Input
                type="time"
                value={settings.businessHoursEnd}
                onChange={(e) => updateSetting('businessHoursEnd', e.target.value)}
              />
            </div>
          </div>

          {/* Business Days */}
          <div className="space-y-3">
            <Label>Business Days</Label>
            <div className="grid grid-cols-4 gap-2">
              {DAYS_OF_WEEK.map((day) => (
                <div key={day.value} className="flex items-center space-x-2">
                  <Switch
                    checked={settings.businessDays.includes(day.value)}
                    onCheckedChange={() => toggleBusinessDay(day.value)}
                  />
                  <Label className="text-sm">{day.label}</Label>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Routing Policies */}
      <Card>
        <CardHeader>
          <CardTitle>Routing Policies</CardTitle>
          <CardDescription>
            Configure how tasks are routed outside business hours
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>After Hours Routing</Label>
              <div className="text-sm text-muted-foreground">
                Allow task assignment outside business hours
              </div>
            </div>
            <Switch
              checked={settings.afterHoursRouting}
              onCheckedChange={(checked) => updateSetting('afterHoursRouting', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Weekend Routing</Label>
              <div className="text-sm text-muted-foreground">
                Allow task assignment on weekends
              </div>
            </div>
            <Switch
              checked={settings.weekendRouting}
              onCheckedChange={(checked) => updateSetting('weekendRouting', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Urgent Tasks Override</Label>
              <div className="text-sm text-muted-foreground">
                Allow urgent tasks to override working hours restrictions
              </div>
            </div>
            <Switch
              checked={settings.urgentTasksOverride}
              onCheckedChange={(checked) => updateSetting('urgentTasksOverride', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Override Agent Hours</Label>
              <div className="text-sm text-muted-foreground">
                Use organization business hours instead of individual agent schedules
              </div>
            </div>
            <Switch
              checked={settings.overrideAgentHours}
              onCheckedChange={(checked) => updateSetting('overrideAgentHours', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex items-center gap-2">
        <Button onClick={saveSettings} disabled={saving}>
          {saving ? (
            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Save className="h-4 w-4 mr-2" />
          )}
          Save Settings
        </Button>
        <Button variant="outline" onClick={fetchSettings}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Reset
        </Button>
      </div>
    </div>
  );
}
