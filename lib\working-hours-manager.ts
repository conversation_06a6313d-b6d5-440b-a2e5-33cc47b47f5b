import { prisma } from "@/lib/prisma";
import {
  AgentWorkingHours,
  AgentAvailability,
  OrganizationSettings,
} from "@prisma/client";
import {
  OrganizationWorkingSettings,
  SetWorkingHoursRequest,
  AvailabilityCheckResponse,
} from "@/lib/types";

export class WorkingHoursManager {
  /**
   * Set agent's weekly working hours schedule
   */
  async setAgentWorkingHours(
    agentId: string,
    schedule: SetWorkingHoursRequest["schedule"]
  ): Promise<AgentWorkingHours[]> {
    // Delete existing working hours for this agent
    await prisma.agentWorkingHours.deleteMany({
      where: { agentId },
    });

    // Create new working hours
    const workingHours = await Promise.all(
      schedule.map((day) =>
        prisma.agentWorkingHours.create({
          data: {
            agentId,
            dayOfWeek: day.dayOfWeek,
            startTime: day.startTime,
            endTime: day.endTime,
            timezone: day.timezone || "UTC",
            isActive: day.isActive ?? true,
          },
        })
      )
    );

    return workingHours;
  }

  /**
   * Get agent's working hours schedule
   */
  async getAgentWorkingHours(agentId: string): Promise<AgentWorkingHours[]> {
    return prisma.agentWorkingHours.findMany({
      where: { agentId },
      orderBy: { dayOfWeek: "asc" },
    });
  }

  /**
   * Update working hours for a specific day
   */
  async updateWorkingDay(
    agentId: string,
    dayOfWeek: number,
    startTime: string,
    endTime: string,
    timezone?: string
  ): Promise<AgentWorkingHours> {
    return prisma.agentWorkingHours.upsert({
      where: {
        agentId_dayOfWeek: { agentId, dayOfWeek },
      },
      update: {
        startTime,
        endTime,
        timezone: timezone || "UTC",
        isActive: true,
      },
      create: {
        agentId,
        dayOfWeek,
        startTime,
        endTime,
        timezone: timezone || "UTC",
        isActive: true,
      },
    });
  }

  /**
   * Set agent's current availability status
   */
  async setAgentAvailability(
    agentId: string,
    status: string,
    reason?: string,
    duration?: number
  ): Promise<AgentAvailability> {
    const endTime = duration
      ? new Date(Date.now() + duration * 60 * 1000)
      : null;

    // Delete any existing availability record for this agent
    await prisma.agentAvailability.deleteMany({
      where: { agentId },
    });

    // Create new availability record
    return prisma.agentAvailability.create({
      data: {
        agentId,
        status,
        reason,
        startTime: new Date(),
        endTime,
        isTemporary: !!duration,
      },
    });
  }

  /**
   * Get agent's current availability status
   */
  async getAgentAvailability(agentId: string): Promise<AgentAvailability | null> {
    const availability = await prisma.agentAvailability.findFirst({
      where: { agentId },
      orderBy: { createdAt: "desc" },
    });

    // Check if temporary status has expired
    if (
      availability?.isTemporary &&
      availability.endTime &&
      new Date() > availability.endTime
    ) {
      // Status has expired, set back to available
      return this.setAgentAvailability(agentId, "available");
    }

    return availability;
  }

  /**
   * Set temporary unavailability period
   */
  async setTemporaryUnavailability(
    agentId: string,
    startTime: Date,
    endTime: Date,
    reason: string
  ): Promise<AgentAvailability> {
    // Delete any existing availability record
    await prisma.agentAvailability.deleteMany({
      where: { agentId },
    });

    return prisma.agentAvailability.create({
      data: {
        agentId,
        status: "away",
        reason,
        startTime,
        endTime,
        isTemporary: true,
      },
    });
  }

  /**
   * Check if agent is available at specific time
   */
  async isAgentAvailable(
    agentId: string,
    checkTime: Date = new Date()
  ): Promise<boolean> {
    // 1. Check current availability status
    const availability = await this.getAgentAvailability(agentId);
    if (availability && availability.status !== "available") {
      // Check if temporary status has expired
      if (
        availability.isTemporary &&
        availability.endTime &&
        checkTime > availability.endTime
      ) {
        await this.setAgentAvailability(agentId, "available");
        return true;
      }
      return false;
    }

    // 2. Check working hours
    const isInWorkingHours = await this.isWithinWorkingHours(agentId, checkTime);
    if (!isInWorkingHours) {
      // Check organization settings for after-hours routing
      const agent = await prisma.user.findUnique({
        where: { id: agentId },
        select: { organizationId: true },
      });
      
      if (agent) {
        const orgSettings = await this.getOrganizationSettings(agent.organizationId);
        if (!orgSettings.afterHoursRouting) return false;
      }
    }

    // 3. Check capacity
    const agent = await prisma.user.findUnique({
      where: { id: agentId },
      select: { currentTaskCount: true, maxConcurrentTasks: true },
    });

    if (!agent) return false;

    return agent.currentTaskCount < agent.maxConcurrentTasks;
  }

  /**
   * Check if time falls within agent's working hours
   */
  async isWithinWorkingHours(
    agentId: string,
    checkTime: Date = new Date()
  ): Promise<boolean> {
    const agent = await prisma.user.findUnique({
      where: { id: agentId },
      include: { working_hours: true },
    });

    if (!agent) return false;

    // Check organization settings first
    const orgSettings = await this.getOrganizationSettings(agent.organizationId);

    // If organization overrides agent hours, use organization business hours
    if (orgSettings.overrideAgentHours) {
      return this.isWithinOrganizationHours(agent.organizationId, checkTime);
    }

    // If agent has no individual schedule, use organization defaults
    if (agent.working_hours.length === 0) {
      return this.isWithinOrganizationHours(agent.organizationId, checkTime);
    }

    // Use agent's individual working hours
    // Convert check time to agent's timezone
    const agentTime = await this.convertToAgentTimezone(agentId, checkTime);
    const dayOfWeek = agentTime.getDay();
    const timeString = agentTime.toTimeString().substring(0, 5); // "HH:MM"

    const daySchedule = agent.working_hours.find(
      (wh) => wh.dayOfWeek === dayOfWeek && wh.isActive
    );

    if (!daySchedule) return false;

    return timeString >= daySchedule.startTime && timeString <= daySchedule.endTime;
  }

  /**
   * Get list of currently available agents
   */
  async getAvailableAgents(
    organizationId: string,
    checkTime: Date = new Date()
  ): Promise<string[]> {
    const agents = await prisma.user.findMany({
      where: { organizationId },
      select: { id: true },
    });

    const availableAgents: string[] = [];

    for (const agent of agents) {
      const isAvailable = await this.isAgentAvailable(agent.id, checkTime);
      if (isAvailable) {
        availableAgents.push(agent.id);
      }
    }

    return availableAgents;
  }

  /**
   * Convert UTC time to agent's timezone
   */
  async convertToAgentTimezone(agentId: string, utcTime: Date): Promise<Date> {
    // Get agent timezone for future implementation
    await prisma.user.findUnique({
      where: { id: agentId },
      select: { default_timezone: true },
    });

    // For now, return the UTC time as-is
    // In a real implementation, you'd use a library like date-fns-tz or moment-timezone
    // The timezone would be used here: agent?.default_timezone || "UTC"
    return utcTime;
  }

  /**
   * Find next time agent will be available
   */
  async getNextAvailableTime(
    agentId: string,
    fromTime: Date = new Date()
  ): Promise<Date | null> {
    // Check if agent is currently available
    if (await this.isAgentAvailable(agentId, fromTime)) {
      return fromTime;
    }

    // Check availability status
    const availability = await this.getAgentAvailability(agentId);
    if (availability?.isTemporary && availability.endTime) {
      return availability.endTime;
    }

    // Find next working hours slot
    const workingHours = await this.getAgentWorkingHours(agentId);
    if (workingHours.length === 0) {
      return null; // No schedule set
    }

    // Simple implementation: find next working day
    const currentDay = fromTime.getDay();
    const nextWorkingDay = workingHours.find(
      (wh) => wh.dayOfWeek > currentDay && wh.isActive
    );

    if (nextWorkingDay) {
      const nextDate = new Date(fromTime);
      nextDate.setDate(nextDate.getDate() + (nextWorkingDay.dayOfWeek - currentDay));
      const [hours, minutes] = nextWorkingDay.startTime.split(":").map(Number);
      nextDate.setHours(hours, minutes, 0, 0);
      return nextDate;
    }

    return null; // No next available time found
  }

  /**
   * Check if time falls within organization business hours
   */
  async isWithinOrganizationHours(
    organizationId: string,
    checkTime: Date = new Date()
  ): Promise<boolean> {
    const orgSettings = await this.getOrganizationSettings(organizationId);

    const dayOfWeek = checkTime.getDay();
    const timeString = checkTime.toTimeString().substring(0, 5); // "HH:MM"

    // Check if it's a business day
    if (!orgSettings.businessDays.includes(dayOfWeek)) {
      return orgSettings.weekendRouting;
    }

    // Check if it's within business hours
    return (
      timeString >= orgSettings.businessHoursStart &&
      timeString <= orgSettings.businessHoursEnd
    );
  }

  /**
   * Get organization working settings
   */
  async getOrganizationSettings(
    organizationId: string
  ): Promise<OrganizationWorkingSettings> {
    const settings = await prisma.organizationSettings.findUnique({
      where: { organizationId },
    });

    if (!settings) {
      // Return default settings
      return {
        id: "",
        organizationId,
        defaultTimezone: "UTC",
        businessHoursStart: "09:00",
        businessHoursEnd: "17:00",
        businessDays: [1, 2, 3, 4, 5], // Monday-Friday
        afterHoursRouting: false,
        weekendRouting: false,
        urgentTasksOverride: true,
        overrideAgentHours: false,
      };
    }

    return {
      id: settings.id,
      organizationId: settings.organizationId,
      defaultTimezone: settings.defaultTimezone,
      businessHoursStart: settings.businessHoursStart,
      businessHoursEnd: settings.businessHoursEnd,
      businessDays: settings.businessDays as number[],
      afterHoursRouting: settings.afterHoursRouting,
      weekendRouting: settings.weekendRouting,
      urgentTasksOverride: settings.urgentTasksOverride,
      overrideAgentHours: settings.overrideAgentHours,
    };
  }

  /**
   * Update organization working settings
   */
  async updateOrganizationSettings(
    organizationId: string,
    settings: Partial<OrganizationWorkingSettings>
  ): Promise<OrganizationSettings> {
    return prisma.organizationSettings.upsert({
      where: { organizationId },
      update: {
        defaultTimezone: settings.defaultTimezone,
        businessHoursStart: settings.businessHoursStart,
        businessHoursEnd: settings.businessHoursEnd,
        businessDays: settings.businessDays,
        afterHoursRouting: settings.afterHoursRouting,
        weekendRouting: settings.weekendRouting,
        urgentTasksOverride: settings.urgentTasksOverride,
        overrideAgentHours: settings.overrideAgentHours,
      },
      create: {
        organizationId,
        defaultTimezone: settings.defaultTimezone || "UTC",
        businessHoursStart: settings.businessHoursStart || "09:00",
        businessHoursEnd: settings.businessHoursEnd || "17:00",
        businessDays: settings.businessDays || [1, 2, 3, 4, 5],
        afterHoursRouting: settings.afterHoursRouting || false,
        weekendRouting: settings.weekendRouting || false,
        urgentTasksOverride: settings.urgentTasksOverride ?? true,
        overrideAgentHours: settings.overrideAgentHours || false,
      },
    });
  }

  /**
   * Bulk check agent availability
   */
  async checkAgentsAvailability(
    agentIds: string[],
    checkTime: Date = new Date()
  ): Promise<AvailabilityCheckResponse[]> {
    const results: AvailabilityCheckResponse[] = [];

    for (const agentId of agentIds) {
      const isAvailable = await this.isAgentAvailable(agentId, checkTime);
      let reason: string | undefined;
      let nextAvailableTime: Date | null = null;

      if (!isAvailable) {
        const availability = await this.getAgentAvailability(agentId);
        reason = availability?.reason || "Not available";
        nextAvailableTime = await this.getNextAvailableTime(agentId, checkTime);
      }

      results.push({
        agentId,
        isAvailable,
        reason,
        nextAvailableTime: nextAvailableTime?.toISOString(),
      });
    }

    return results;
  }
}

/**
 * Create a new WorkingHoursManager instance
 */
export function createWorkingHoursManager(): WorkingHoursManager {
  return new WorkingHoursManager();
}
