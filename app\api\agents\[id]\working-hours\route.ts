import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { createWorkingHoursManager } from "@/lib/working-hours-manager";
import { SetWorkingHoursRequest } from "@/lib/types";

/**
 * GET /api/agents/[id]/working-hours
 * Get agent's working hours schedule
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const agentId = params.id;
    const workingHoursManager = createWorkingHoursManager();

    // Check if user can access this agent's data
    if (session.user.role !== "ADMIN" && session.user.id !== agentId) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const workingHours = await workingHoursManager.getAgentWorkingHours(agentId);

    return NextResponse.json({
      success: true,
      data: workingHours,
    });
  } catch (error) {
    console.error("Error fetching working hours:", error);
    return NextResponse.json(
      { error: "Failed to fetch working hours" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/agents/[id]/working-hours
 * Set agent's working hours schedule
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const agentId = params.id;
    const workingHoursManager = createWorkingHoursManager();

    // Check if user can modify this agent's data
    if (session.user.role !== "ADMIN" && session.user.id !== agentId) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body: SetWorkingHoursRequest = await request.json();

    // Validate request body
    if (!body.schedule || !Array.isArray(body.schedule)) {
      return NextResponse.json(
        { error: "Invalid schedule format" },
        { status: 400 }
      );
    }

    // Validate each schedule entry
    for (const day of body.schedule) {
      if (
        typeof day.dayOfWeek !== "number" ||
        day.dayOfWeek < 0 ||
        day.dayOfWeek > 6 ||
        typeof day.startTime !== "string" ||
        typeof day.endTime !== "string" ||
        !day.startTime.match(/^\d{2}:\d{2}$/) ||
        !day.endTime.match(/^\d{2}:\d{2}$/)
      ) {
        return NextResponse.json(
          { error: "Invalid schedule entry format" },
          { status: 400 }
        );
      }
    }

    const workingHours = await workingHoursManager.setAgentWorkingHours(
      agentId,
      body.schedule
    );

    return NextResponse.json({
      success: true,
      data: workingHours,
      message: "Working hours updated successfully",
    });
  } catch (error) {
    console.error("Error setting working hours:", error);
    return NextResponse.json(
      { error: "Failed to set working hours" },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/agents/[id]/working-hours
 * Update specific day working hours
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const agentId = params.id;
    const workingHoursManager = createWorkingHoursManager();

    // Check if user can modify this agent's data
    if (session.user.role !== "ADMIN" && session.user.id !== agentId) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const { dayOfWeek, startTime, endTime, timezone } = body;

    // Validate request body
    if (
      typeof dayOfWeek !== "number" ||
      dayOfWeek < 0 ||
      dayOfWeek > 6 ||
      typeof startTime !== "string" ||
      typeof endTime !== "string" ||
      !startTime.match(/^\d{2}:\d{2}$/) ||
      !endTime.match(/^\d{2}:\d{2}$/)
    ) {
      return NextResponse.json(
        { error: "Invalid request format" },
        { status: 400 }
      );
    }

    const workingHours = await workingHoursManager.updateWorkingDay(
      agentId,
      dayOfWeek,
      startTime,
      endTime,
      timezone
    );

    return NextResponse.json({
      success: true,
      data: workingHours,
      message: "Working day updated successfully",
    });
  } catch (error) {
    console.error("Error updating working day:", error);
    return NextResponse.json(
      { error: "Failed to update working day" },
      { status: 500 }
    );
  }
}
