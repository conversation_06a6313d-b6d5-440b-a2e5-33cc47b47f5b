import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { createSkillsManager } from '@/lib/skills-utils';
import { AddAgentSkillRequest } from '@/lib/types';

/**
 * GET /api/agents/[id]/skills
 * Get all skills for a specific agent
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const agentId = params.id;

    // Users can only view their own skills unless they're admin
    if (session.user.role !== 'ADMIN' && session.user.id !== agentId) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const skillsManager = createSkillsManager();
    const skills = await skillsManager.getAgentSkills(agentId);

    return NextResponse.json({
      success: true,
      data: skills,
    });
  } catch (error) {
    console.error('Error fetching agent skills:', error);
    return NextResponse.json(
      { error: 'Failed to fetch agent skills' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/agents/[id]/skills
 * Add a skill to an agent
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const agentId = params.id;

    // Only admins can add skills to agents (for now)
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body: AddAgentSkillRequest = await request.json();

    // Validate required fields
    if (!body.skillName || body.skillName.trim().length === 0) {
      return NextResponse.json(
        { error: 'Skill name is required' },
        { status: 400 }
      );
    }

    if (!body.proficiencyLevel || body.proficiencyLevel < 1 || body.proficiencyLevel > 5) {
      return NextResponse.json(
        { error: 'Proficiency level must be between 1 and 5' },
        { status: 400 }
      );
    }

    const skillsManager = createSkillsManager();
    const skill = await skillsManager.addSkillToAgent(
      agentId,
      body.skillName.trim(),
      body.proficiencyLevel
    );

    return NextResponse.json({
      success: true,
      data: skill,
    }, { status: 201 });
  } catch (error: unknown) {
    console.error('Error adding agent skill:', error);

    if (error instanceof Error && error.message.includes('already has skill')) {
      return NextResponse.json(
        { error: error.message },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to add skill to agent' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/agents/[id]/skills
 * Update an agent's skill proficiency level
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const agentId = params.id;

    // Only admins can update agent skills (for now)
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const { skillName, proficiencyLevel } = body;

    // Validate required fields
    if (!skillName || skillName.trim().length === 0) {
      return NextResponse.json(
        { error: 'Skill name is required' },
        { status: 400 }
      );
    }

    if (!proficiencyLevel || proficiencyLevel < 1 || proficiencyLevel > 5) {
      return NextResponse.json(
        { error: 'Proficiency level must be between 1 and 5' },
        { status: 400 }
      );
    }

    const skillsManager = createSkillsManager();
    const skill = await skillsManager.updateAgentSkillLevel(
      agentId,
      skillName.trim(),
      proficiencyLevel
    );

    return NextResponse.json({
      success: true,
      data: skill,
    });
  } catch (error: unknown) {
    console.error('Error updating agent skill:', error);

    if (error && typeof error === 'object' && 'code' in error && error.code === 'P2025') {
      return NextResponse.json(
        { error: 'Skill not found for this agent' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update agent skill' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/agents/[id]/skills
 * Remove a skill from an agent
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const agentId = params.id;

    // Only admins can remove agent skills (for now)
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const skillName = searchParams.get('skillName');

    if (!skillName) {
      return NextResponse.json(
        { error: 'Skill name is required' },
        { status: 400 }
      );
    }

    const skillsManager = createSkillsManager();
    const removed = await skillsManager.removeSkillFromAgent(agentId, skillName);

    if (!removed) {
      return NextResponse.json(
        { error: 'Skill not found for this agent' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Skill removed successfully',
    });
  } catch (error) {
    console.error('Error removing agent skill:', error);
    return NextResponse.json(
      { error: 'Failed to remove agent skill' },
      { status: 500 }
    );
  }
}
