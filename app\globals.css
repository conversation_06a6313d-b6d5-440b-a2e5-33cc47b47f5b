@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: 36 39% 88%;
    --foreground: 36 45% 15%;
    --primary: 36 45% 70%;
    --primary-foreground: 36 45% 11%;
    --secondary: 40 35% 77%;
    --secondary-foreground: 36 45% 25%;
    --accent: 36 64% 57%;
    --accent-foreground: 36 72% 17%;
    --destructive: 0 84% 37%;
    --destructive-foreground: 0 0% 98%;
    --muted: 36 33% 75%;
    --muted-foreground: 36 45% 25%;
    --card: 36 46% 82%;
    --card-foreground: 36 45% 20%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --border: 36 45% 60%;
    --input: 36 45% 60%;
    --ring: 36 45% 30%;
    --chart-1: 25 34% 28%;
    --chart-2: 26 36% 34%;
    --chart-3: 28 40% 40%;
    --chart-4: 31 41% 48%;
    --chart-5: 35 43% 53%;
  }

  .dark {
    --background: 36 39% 88%;
    --foreground: 36 45% 15%;
    --primary: 36 45% 70%;
    --primary-foreground: 36 45% 11%;
    --secondary: 40 35% 77%;
    --secondary-foreground: 36 45% 25%;
    --accent: 36 64% 57%;
    --accent-foreground: 36 72% 17%;
    --destructive: 0 84% 37%;
    --destructive-foreground: 0 0% 98%;
    --muted: 36 33% 75%;
    --muted-foreground: 36 45% 25%;
    --card: 36 46% 82%;
    --card-foreground: 36 45% 20%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --border: 36 45% 60%;
    --input: 36 45% 60%;
    --ring: 36 45% 30%;
    --chart-1: 25 34% 28%;
    --chart-2: 26 36% 34%;
    --chart-3: 28 40% 40%;
    --chart-4: 31 41% 48%;
    --chart-5: 35 43% 53%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Fix for dnd-kit drag overlay z-index issues */
.dnd-overlay,
[data-dnd-overlay],
[data-dnd-context] [data-dnd-overlay] {
  z-index: 999999 !important;
  position: fixed !important;
  pointer-events: none !important;
}

.dnd-overlay > *,
[data-dnd-overlay] > * {
  z-index: 999999 !important;
  pointer-events: none !important;
}

/* Ensure drag overlay appears above everything */
body > div[data-dnd-overlay] {
  z-index: 999999 !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  pointer-events: none !important;
}
