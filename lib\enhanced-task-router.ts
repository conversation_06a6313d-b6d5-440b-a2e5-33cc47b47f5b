import { prisma } from './prisma'
import type { 
  R<PERSON>ing<PERSON>ontext, 
  R<PERSON>ing<PERSON><PERSON><PERSON>, 
  AgentWithSkills, 
  TaskWithSkills,
  ScoredAgent,
  EnhancedRoutingStrategy,
  AgentWithPerformance
} from './types'
import type { AgentSkill, TaskRequiredSkill } from '@prisma/client'

// ============================================================================
// ENHANCED TASK ROUTER CLASS
// ============================================================================

export class EnhancedTaskRouter {
  private organizationId: string

  constructor(organizationId: string) {
    this.organizationId = organizationId
  }

  /**
   * Main enhanced routing method
   */
  async routeTask(context: RoutingContext): Promise<RoutingResult> {
    try {
      const strategy = context.strategy || await this.getDefaultStrategy(context.organizationId)
      
      switch (strategy) {
        case 'weighted_round_robin':
          return this.weightedRoundRobin(context)
        case 'best_skill_match':
          return this.bestSkillMatch(context)
        case 'performance_based':
          return this.performanceBased(context)
        case 'hybrid_intelligent':
          return this.hybridIntelligent(context)
        default:
          return this.weightedRoundRobin(context)
      }
    } catch (error) {
      console.error('Enhanced routing error:', error)
      // Fallback to first available agent
      return this.fallbackRouting(context)
    }
  }

  /**
   * Enhanced weighted round robin with skill matching
   */
  async weightedRoundRobin(context: RoutingContext): Promise<RoutingResult> {
    const { task, availableAgents } = context
    const reasoning: string[] = ['Using weighted round robin strategy']

    const scoredAgents = availableAgents.map(agent => {
      const workloadScore = this.calculateWorkloadScore(agent)
      const skillScore = this.calculateSkillMatchScore(agent.skills, task.requiredSkills)
      const performanceScore = this.calculatePerformanceScore(agent)
      
      const totalScore = (workloadScore * 0.4) + (skillScore * 0.4) + (performanceScore * 0.2)
      
      return {
        agent,
        score: totalScore,
        breakdown: {
          skillScore,
          workloadScore,
          performanceScore
        }
      }
    })

    reasoning.push(`Evaluated ${scoredAgents.length} agents with workload (40%), skills (40%), performance (20%) weighting`)

    return this.selectByWeight(scoredAgents, task, reasoning)
  }

  /**
   * Best skill match strategy - prioritizes skill matching
   */
  async bestSkillMatch(context: RoutingContext): Promise<RoutingResult> {
    const { task, availableAgents } = context
    const reasoning: string[] = ['Using best skill match strategy']

    const scoredAgents = availableAgents.map(agent => {
      const skillScore = this.calculateSkillMatchScore(agent.skills, task.requiredSkills)
      const workloadScore = this.calculateWorkloadScore(agent)
      
      const totalScore = (skillScore * 0.8) + (workloadScore * 0.2)
      
      return {
        agent,
        score: totalScore,
        breakdown: {
          skillScore,
          workloadScore,
          performanceScore: 0
        }
      }
    })

    reasoning.push(`Prioritized skill matching (80%) over workload (20%)`)

    return this.selectBestScore(scoredAgents, task, reasoning)
  }

  /**
   * Performance-based strategy - prioritizes historical performance
   */
  async performanceBased(context: RoutingContext): Promise<RoutingResult> {
    const { task, availableAgents } = context
    const reasoning: string[] = ['Using performance-based strategy']

    const scoredAgents = availableAgents.map(agent => {
      const performanceScore = this.calculatePerformanceScore(agent)
      const skillScore = this.calculateSkillMatchScore(agent.skills, task.requiredSkills)
      const workloadScore = this.calculateWorkloadScore(agent)
      
      const totalScore = (performanceScore * 0.5) + (skillScore * 0.3) + (workloadScore * 0.2)
      
      return {
        agent,
        score: totalScore,
        breakdown: {
          skillScore,
          workloadScore,
          performanceScore
        }
      }
    })

    reasoning.push(`Prioritized performance (50%), skills (30%), workload (20%)`)

    return this.selectBestScore(scoredAgents, task, reasoning)
  }

  /**
   * Hybrid intelligent strategy - dynamic weighting based on task characteristics
   */
  async hybridIntelligent(context: RoutingContext): Promise<RoutingResult> {
    const { task, availableAgents } = context
    const reasoning: string[] = ['Using hybrid intelligent strategy']
    const urgencyWeight = this.calculateUrgencyWeight(task)

    // Dynamic weighting based on urgency and task characteristics
    let skillWeight = 0.4
    let performanceWeight = 0.3
    let workloadWeight = 0.3

    if (urgencyWeight > 0.8) {
      // High urgency: prioritize performance and availability
      performanceWeight = 0.5
      workloadWeight = 0.4
      skillWeight = 0.1
      reasoning.push('High urgency detected - prioritizing performance and availability')
    } else if (task.requiredSkills.length > 0) {
      // Skill-specific task: prioritize skill match
      skillWeight = 0.6
      performanceWeight = 0.2
      workloadWeight = 0.2
      reasoning.push('Skill requirements detected - prioritizing skill matching')
    }

    const scoredAgents = availableAgents.map(agent => {
      const skillScore = this.calculateSkillMatchScore(agent.skills, task.requiredSkills)
      const workloadScore = this.calculateWorkloadScore(agent)
      const performanceScore = this.calculatePerformanceScore(agent)

      const totalScore = (skillScore * skillWeight) +
                        (performanceScore * performanceWeight) +
                        (workloadScore * workloadWeight)

      return {
        agent,
        score: totalScore,
        breakdown: {
          skillScore,
          workloadScore,
          performanceScore,
          urgencyScore: urgencyWeight
        }
      }
    })

    reasoning.push(`Dynamic weighting: skills (${Math.round(skillWeight * 100)}%), performance (${Math.round(performanceWeight * 100)}%), workload (${Math.round(workloadWeight * 100)}%)`)

    return this.selectByWeight(scoredAgents, task, reasoning)
  }

  /**
   * Calculate skill match score (0-1)
   */
  calculateSkillMatchScore(agentSkills: AgentSkill[], requiredSkills: TaskRequiredSkill[]): number {
    if (requiredSkills.length === 0) return 1.0 // No requirements = perfect match
    
    let totalMatch = 0
    let totalRequired = 0
    
    for (const required of requiredSkills) {
      const agentSkill = agentSkills.find(s => s.skillName === required.skillName)
      
      if (agentSkill) {
        // Calculate match quality based on proficiency vs requirement
        const matchQuality = Math.min(agentSkill.proficiencyLevel / required.requiredLevel, 1.0)
        totalMatch += matchQuality
      }
      totalRequired += 1
    }
    
    return totalMatch / totalRequired
  }

  /**
   * Calculate workload score (0-1, higher is better)
   */
  calculateWorkloadScore(agent: AgentWithSkills): number {
    const utilization = agent.currentTaskCount / agent.maxConcurrentTasks
    return Math.max(0, 1 - utilization) // Higher score for lower utilization
  }

  /**
   * Calculate performance score (0-1)
   */
  calculatePerformanceScore(agent: AgentWithSkills): number {
    const agentWithPerf = agent as AgentWithPerformance
    if (!agentWithPerf.performance) return 0.5 // Default score for new agents
    
    const completionScore = agentWithPerf.performance.completionRate
    const qualityScore = agentWithPerf.performance.qualityScore / 100
    const responseScore = Math.max(0, 1 - (agentWithPerf.performance.avgResponseTime / 60)) // Normalize to hours
    
    return (completionScore * 0.4) + (qualityScore * 0.4) + (responseScore * 0.2)
  }

  /**
   * Calculate urgency weight based on task priority
   */
  calculateUrgencyWeight(task: TaskWithSkills): number {
    switch (task.priority) {
      case 'URGENT': return 1.0
      case 'HIGH': return 0.8
      case 'MEDIUM': return 0.5
      case 'LOW': return 0.2
      default: return 0.5
    }
  }

  /**
   * Get default routing strategy for organization
   */
  private async getDefaultStrategy(organizationId: string): Promise<EnhancedRoutingStrategy> {
    const defaultStrategy = await prisma.routing_strategies.findFirst({
      where: {
        organization_id: organizationId,
        is_default: true,
        is_active: true
      }
    })
    
    return (defaultStrategy?.strategy as EnhancedRoutingStrategy) || 'weighted_round_robin'
  }

  /**
   * Select agent by weighted random selection
   */
  private selectByWeight(scoredAgents: ScoredAgent[], task: TaskWithSkills, reasoning: string[]): RoutingResult {
    if (scoredAgents.length === 0) {
      throw new Error('No agents available for selection')
    }

    // Sort by score for alternatives
    const sortedAgents = [...scoredAgents].sort((a, b) => b.score - a.score)
    
    // Weighted random selection
    const totalWeight = scoredAgents.reduce((sum, sa) => sum + sa.score, 0)
    let random = Math.random() * totalWeight
    
    let selectedAgent = sortedAgents[0].agent // fallback
    for (const { agent, score } of scoredAgents) {
      random -= score
      if (random <= 0) {
        selectedAgent = agent
        break
      }
    }

    const selectedScore = scoredAgents.find(sa => sa.agent.id === selectedAgent.id)?.score || 0
    const confidence = Math.round(selectedScore * 100)
    
    reasoning.push(`Selected agent with confidence score: ${confidence}%`)
    
    return {
      selectedAgent,
      confidence,
      reasoning,
      alternativeAgents: sortedAgents.slice(0, 3).map(sa => sa.agent).filter(a => a.id !== selectedAgent.id)
    }
  }

  /**
   * Select agent with best score
   */
  private selectBestScore(scoredAgents: ScoredAgent[], task: TaskWithSkills, reasoning: string[]): RoutingResult {
    if (scoredAgents.length === 0) {
      throw new Error('No agents available for selection')
    }

    const sortedAgents = [...scoredAgents].sort((a, b) => b.score - a.score)
    const selectedAgent = sortedAgents[0].agent
    const confidence = Math.round(sortedAgents[0].score * 100)
    
    reasoning.push(`Selected highest scoring agent with confidence: ${confidence}%`)
    
    return {
      selectedAgent,
      confidence,
      reasoning,
      alternativeAgents: sortedAgents.slice(1, 4).map(sa => sa.agent)
    }
  }

  /**
   * Fallback routing when enhanced routing fails
   */
  private fallbackRouting(context: RoutingContext): RoutingResult {
    const { availableAgents } = context
    
    if (availableAgents.length === 0) {
      throw new Error('No agents available for fallback routing')
    }

    return {
      selectedAgent: availableAgents[0],
      confidence: 50,
      reasoning: ['Fallback routing - selected first available agent'],
      alternativeAgents: availableAgents.slice(1, 3)
    }
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Create an enhanced task router instance
 */
export function createEnhancedTaskRouter(organizationId: string): EnhancedTaskRouter {
  return new EnhancedTaskRouter(organizationId)
}
