import {
  Organization,
  User,
  Task,
  TaskEvent,
  UserStatus,
  UserRole,
  TaskStatus,
  TaskPriority,
  TaskEventType,
  AgentSkill,
  TaskRequiredSkill,
  SkillsCatalog,
  AgentWorkingHours,
  AgentAvailability,
  OrganizationSettings,
} from "@prisma/client";

// ============================================================================
// EXTENDED TYPES WITH RELATIONS
// ============================================================================

export type OrganizationWithRelations = Organization & {
  users?: User[];
  tasks?: Task[];
  _count?: {
    users: number;
    tasks: number;
  };
};

export type UserWithRelations = User & {
  organization?: Organization;
  assignedTasks?: Task[];
  taskEvents?: TaskEvent[];
  _count?: {
    assignedTasks: number;
    taskEvents: number;
  };
};

export type TaskWithRelations = Task & {
  organization?: Organization;
  assignedUser?: User | null;
  events?: TaskEvent[];
  _count?: {
    events: number;
  };
};

export type TaskEventWithRelations = TaskEvent & {
  task?: Task;
  user?: User | null;
};

// ============================================================================
// API TYPES
// ============================================================================

export interface CreateTaskRequest {
  title: string;
  description?: string;
  priority?: TaskPriority;
  type?: string;
  estimatedDuration?: number;
  source?: string;
  responseDeadline?: Date; // Will be mapped to response_by in db-utils
  slaDeadline?: Date; // Will be mapped to resolve_by in db-utils
}

export interface UpdateTaskRequest {
  title?: string;
  description?: string;
  priority?: TaskPriority;
  type?: string;
  estimatedDuration?: number;
  status?: TaskStatus;
  assignedTo?: string | null;
}

export interface CreateUserRequest {
  name: string;
  email: string;
  password?: string;
  role?: UserRole;
  maxConcurrentTasks?: number;
  status?: UserStatus;
}

export interface UpdateUserRequest {
  name?: string;
  email?: string;
  role?: UserRole;
  maxConcurrentTasks?: number;
  status?: UserStatus;
}

export interface CreateOrganizationRequest {
  name: string;
}

// ============================================================================
// AUTHENTICATION TYPES
// ============================================================================

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
  organizationId?: string;
  role?: UserRole;
}

export interface AuthUser {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  organizationId: string;
  image?: string;
}

// ============================================================================
// DASHBOARD TYPES
// ============================================================================

export interface DashboardStats {
  totalTasks: number;
  pendingTasks: number;
  assignedTasks: number;
  completedTasks: number;
  totalUsers: number;
  availableUsers: number;
  avgResponseTime?: number;
  avgCompletionTime?: number;
}

export interface UserWorkload {
  userId: string;
  userName: string;
  currentTasks: number;
  maxTasks: number;
  utilizationRate: number;
  status: UserStatus;
}

export interface TaskDistribution {
  priority: TaskPriority;
  count: number;
  percentage: number;
}

// ============================================================================
// ROUTING TYPES (for future phases)
// ============================================================================

export interface Assignment {
  taskId: string;
  agentId: string;
  assignedAt: Date;
  confidence: number;
}

export type RoutingStrategy =
  | "round_robin"
  | "weighted_round_robin"
  | "least_loaded"
  | "best_match";

// ============================================================================
// FILTER TYPES
// ============================================================================

export interface TaskFilters {
  status?: TaskStatus[];
  priority?: TaskPriority[];
  type?: string[];
  assignedTo?: string[];
  dateRange?: {
    from: Date;
    to: Date;
  };
}

export interface UserFilters {
  status?: UserStatus[];
  organizationId?: string;
}

// ============================================================================
// PAGINATION TYPES
// ============================================================================

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// ============================================================================
// RE-EXPORT PRISMA ENUMS
// ============================================================================

export {
  UserStatus,
  UserRole,
  TaskStatus,
  TaskPriority,
  TaskEventType,
  type Organization,
  type User,
  type Task,
  type TaskEvent,
  type AgentSkill,
  type TaskRequiredSkill,
  type SkillsCatalog,
  type AgentWorkingHours,
  type AgentAvailability,
  type OrganizationSettings,
};

// ============================================================================
// SKILLS MANAGEMENT TYPES (Phase 2)
// ============================================================================

export interface AgentWithSkills extends User {
  skills: AgentSkill[];
}

export interface TaskWithSkills extends Task {
  requiredSkills: TaskRequiredSkill[];
}

// ============================================================================
// WORKING HOURS & AVAILABILITY TYPES (Phase 2 - Task 3)
// ============================================================================

export interface WorkingHours {
  id: string;
  agentId: string;
  dayOfWeek: number;
  startTime: string;
  endTime: string;
  timezone: string;
  isActive: boolean;
}

export interface AvailabilityStatus {
  id: string;
  agentId: string;
  status: 'available' | 'busy' | 'away' | 'offline';
  reason?: string;
  startTime?: Date;
  endTime?: Date;
  isTemporary: boolean;
}

export interface OrganizationWorkingSettings {
  id: string;
  organizationId: string;
  defaultTimezone: string;
  businessHoursStart: string;
  businessHoursEnd: string;
  businessDays: number[];
  afterHoursRouting: boolean;
  weekendRouting: boolean;
  urgentTasksOverride: boolean;
  overrideAgentHours: boolean;
}

export interface AgentWithWorkingHours extends User {
  working_hours: AgentWorkingHours[];
  availability: AgentAvailability[];
}

export interface SetWorkingHoursRequest {
  schedule: {
    dayOfWeek: number;
    startTime: string;
    endTime: string;
    timezone?: string;
    isActive?: boolean;
  }[];
}

export interface SetAvailabilityRequest {
  status: 'available' | 'busy' | 'away' | 'offline';
  reason?: string;
  duration?: number; // minutes
}

export interface AvailabilityCheckRequest {
  agentIds: string[];
  checkTime?: string; // ISO string
}

export interface AvailabilityCheckResponse {
  agentId: string;
  isAvailable: boolean;
  reason?: string;
  nextAvailableTime?: string; // ISO string
}

export interface OrganizationWithSkills extends Organization {
  skillsCatalog: SkillsCatalog[];
}

export interface SkillMatch {
  skillName: string;
  required: number;
  actual: number;
  match: boolean;
}

export interface AgentSkillMatch {
  agent: UserWithRelations;
  skillMatches: SkillMatch[];
  overallMatch: number; // percentage 0-100
  missingSkills: string[];
}

export interface SkillRequirement {
  skillName: string;
  requiredLevel: number;
}

export interface CreateSkillRequest {
  name: string;
  description?: string;
  category?: string;
}

export interface AddAgentSkillRequest {
  skillName: string;
  proficiencyLevel: number;
}

export interface AddTaskSkillRequest {
  skillName: string;
  requiredLevel: number;
}

// ============================================================================
// ENHANCED ROUTING TYPES (Phase 2 - Task 2)
// ============================================================================

export interface RoutingContext {
  task: TaskWithSkills;
  availableAgents: AgentWithSkills[];
  organizationId: string;
  strategy?: string;
  urgencyMultiplier?: number;
}

export interface RoutingResult {
  selectedAgent: AgentWithSkills;
  confidence: number; // 0-100
  reasoning: string[];
  alternativeAgents: AgentWithSkills[];
}

export interface AgentWithPerformance extends AgentWithSkills {
  performance?: {
    avgResponseTime: number;
    avgResolutionTime: number;
    completionRate: number;
    qualityScore: number;
    totalTasksCompleted: number;
    totalTasksAssigned: number;
    lastUpdated: Date;
  };
}

export interface ScoredAgent {
  agent: AgentWithSkills;
  score: number;
  breakdown: {
    skillScore: number;
    workloadScore: number;
    performanceScore: number;
    urgencyScore?: number;
  };
}

export type EnhancedRoutingStrategy =
  | 'weighted_round_robin'
  | 'best_skill_match'
  | 'performance_based'
  | 'hybrid_intelligent';

export interface RoutingStrategyConfig {
  id: string;
  organizationId: string;
  name: string;
  strategy: EnhancedRoutingStrategy;
  isDefault: boolean;
  isActive: boolean;
  configuration: Record<string, unknown>;
  createdAt: Date;
}
