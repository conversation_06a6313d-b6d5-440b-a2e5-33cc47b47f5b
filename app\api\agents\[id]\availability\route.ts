import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { createWorkingHoursManager } from "@/lib/working-hours-manager";
import { SetAvailabilityRequest } from "@/lib/types";

/**
 * GET /api/agents/[id]/availability
 * Get agent's current availability status
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const agentId = params.id;
    const workingHoursManager = createWorkingHoursManager();

    // Check if user can access this agent's data
    if (session.user.role !== "ADMIN" && session.user.id !== agentId) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const availability = await workingHoursManager.getAgentAvailability(agentId);
    const isAvailable = await workingHoursManager.isAgentAvailable(agentId);

    return NextResponse.json({
      success: true,
      data: {
        availability,
        isAvailable,
      },
    });
  } catch (error) {
    console.error("Error fetching availability:", error);
    return NextResponse.json(
      { error: "Failed to fetch availability" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/agents/[id]/availability
 * Set agent's availability status
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const agentId = params.id;
    const workingHoursManager = createWorkingHoursManager();

    // Check if user can modify this agent's data
    if (session.user.role !== "ADMIN" && session.user.id !== agentId) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body: SetAvailabilityRequest = await request.json();

    // Validate request body
    const validStatuses = ["available", "busy", "away", "offline"];
    if (!body.status || !validStatuses.includes(body.status)) {
      return NextResponse.json(
        { error: "Invalid status. Must be one of: " + validStatuses.join(", ") },
        { status: 400 }
      );
    }

    if (body.duration && (typeof body.duration !== "number" || body.duration <= 0)) {
      return NextResponse.json(
        { error: "Duration must be a positive number (minutes)" },
        { status: 400 }
      );
    }

    const availability = await workingHoursManager.setAgentAvailability(
      agentId,
      body.status,
      body.reason,
      body.duration
    );

    return NextResponse.json({
      success: true,
      data: availability,
      message: "Availability status updated successfully",
    });
  } catch (error) {
    console.error("Error setting availability:", error);
    return NextResponse.json(
      { error: "Failed to set availability" },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/agents/[id]/availability
 * Update availability with duration
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const agentId = params.id;
    const workingHoursManager = createWorkingHoursManager();

    // Check if user can modify this agent's data
    if (session.user.role !== "ADMIN" && session.user.id !== agentId) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const { startTime, endTime, reason } = body;

    // Validate request body
    if (!startTime || !endTime || !reason) {
      return NextResponse.json(
        { error: "startTime, endTime, and reason are required" },
        { status: 400 }
      );
    }

    const startDate = new Date(startTime);
    const endDate = new Date(endTime);

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return NextResponse.json(
        { error: "Invalid date format" },
        { status: 400 }
      );
    }

    if (endDate <= startDate) {
      return NextResponse.json(
        { error: "End time must be after start time" },
        { status: 400 }
      );
    }

    const availability = await workingHoursManager.setTemporaryUnavailability(
      agentId,
      startDate,
      endDate,
      reason
    );

    return NextResponse.json({
      success: true,
      data: availability,
      message: "Temporary unavailability set successfully",
    });
  } catch (error) {
    console.error("Error setting temporary unavailability:", error);
    return NextResponse.json(
      { error: "Failed to set temporary unavailability" },
      { status: 500 }
    );
  }
}
