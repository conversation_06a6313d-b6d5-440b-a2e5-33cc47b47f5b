import React, { useState } from "react";
import {
  DndContext,
  useDraggable,
  useDroppable,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  rectIntersection,
} from "@dnd-kit/core";
import {
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";

interface Task {
  id: string;
  title: string;
  description?: string;
  priority: "LOW" | "MEDIUM" | "HIGH" | "URGENT";
  status: "PENDING" | "ASSIGNED" | "IN_PROGRESS" | "COMPLETED" | "ESCALATED";
  type: string;
  assignedAt?: string;
  createdAt: string;
  assignedUser?: {
    id: string;
    name: string;
    email: string;
  };
}

interface DraggableTaskProps {
  task: Task;
  onClick: () => void;
  children: React.ReactNode;
}

interface DroppableColumnProps {
  status: string;
  count: number;
  children: React.ReactNode;
}

interface TaskCardContentProps {
  task: Task;
  onEdit: () => void;
  onComplete: () => void;
  onEscalate: () => void;
}

interface AgentTaskDialogProps {
  open: boolean;
  task: Task | null;
  onClose: () => void;
  onSave: (task: Task) => void;
}

interface AgentKanbanBoardProps {
  tasks: Task[];
  onStatusChange: (taskId: string, newStatus: string) => void;
  isLoading?: boolean;
}

const TASK_STATUSES = [
  "PENDING",
  "ASSIGNED",
  "IN_PROGRESS",
  "COMPLETED",
  "ESCALATED",
];

const PRIORITY_COLORS = {
  URGENT: "#ef4444",
  HIGH: "#f59e42",
  MEDIUM: "#fbbf24",
  LOW: "#22c55e",
};

function DraggableTask({ task, onClick, children }: DraggableTaskProps) {
  const { attributes, listeners, setNodeRef, isDragging, transform } = useDraggable({
    id: task.id,
    data: { task },
  });

  const style = {
    transform: transform ? `translate3d(${transform.x}px, ${transform.y}px, 0)` : undefined,
  };

  return (
    <div
      style={{
        ...style,
        background: isDragging ? "#f0f9ff" : "#fff",
        marginBottom: 12,
        padding: 16,
        borderRadius: 12,
        boxShadow: isDragging
          ? "0 8px 32px rgba(59,130,246,0.25)"
          : "0 2px 8px rgba(0,0,0,0.08)",
        opacity: isDragging ? 0 : 1,
        cursor: "pointer",
        transition: isDragging ? "none" : "all 0.2s ease",
        border: isDragging ? "2px solid #3b82f6" : "1px solid #e5e7eb",
        position: "relative",
        userSelect: "none",
        minHeight: 120,
        display: "flex",
        flexDirection: "column",
        zIndex: isDragging ? 9999 : "auto",
      }}
      onClick={onClick}
      tabIndex={0}
      role="button"
      aria-label={`Task: ${task.title}. Click to open details, or drag to move between columns.`}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onClick();
        }
      }}
    >
      <div
        ref={setNodeRef}
        {...attributes}
        {...listeners}
        style={{
          position: "absolute",
          top: 12,
          right: 12,
          width: 28,
          height: 28,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          cursor: isDragging ? "grabbing" : "grab",
          zIndex: 2,
          color: isDragging ? "#3b82f6" : "#9ca3af",
          fontSize: 16,
          background: isDragging ? "#dbeafe" : "#f9fafb",
          borderRadius: 6,
          border: "1px solid",
          borderColor: isDragging ? "#93c5fd" : "#e5e7eb",
          transition: "all 0.2s ease",
        }}
        onClick={e => e.stopPropagation()}
        title="Drag to move between columns"
        aria-label="Drag handle"
        onMouseEnter={e => {
          if (!isDragging) {
            const target = e.target as HTMLElement;
            target.style.background = "#f3f4f6";
            target.style.borderColor = "#d1d5db";
          }
        }}
        onMouseLeave={e => {
          if (!isDragging) {
            const target = e.target as HTMLElement;
            target.style.background = "#f9fafb";
            target.style.borderColor = "#e5e7eb";
          }
        }}
      >
        ⋮⋮
      </div>
      <div style={{ paddingRight: 40 }}>{children}</div>
    </div>
  );
}

function DroppableColumn({ status, count, children }: DroppableColumnProps) {
  const { setNodeRef, isOver } = useDroppable({ id: status });

  const getColumnColor = (status: string) => {
    switch (status) {
      case "PENDING":
        return { bg: "#fef3c7", border: "#f59e0b", text: "#92400e" };
      case "ASSIGNED":
        return { bg: "#e0e7ff", border: "#6366f1", text: "#4338ca" };
      case "IN_PROGRESS":
        return { bg: "#dbeafe", border: "#3b82f6", text: "#1e40af" };
      case "COMPLETED":
        return { bg: "#dcfce7", border: "#10b981", text: "#047857" };
      case "ESCALATED":
        return { bg: "#fee2e2", border: "#ef4444", text: "#dc2626" };
      default:
        return { bg: "#f3f4f6", border: "#9ca3af", text: "#374151" };
    }
  };

  const colors = getColumnColor(status);

  return (
    <div
      ref={setNodeRef}
      style={{
        flex: 1,
        minWidth: 280,
        maxWidth: 320,
        background: isOver ? colors.bg : "#f8fafc",
        borderRadius: 16,
        padding: 16,
        minHeight: 500,
        transition: "all 0.2s ease",
        display: "flex",
        flexDirection: "column",
        border: isOver ? `2px solid ${colors.border}` : "2px solid #e2e8f0",
        boxShadow: isOver
          ? `0 4px 20px ${colors.border}20`
          : "0 2px 8px rgba(0,0,0,0.04)",
        position: "relative",
        zIndex: 1,
      }}
    >
      <div style={{
        fontWeight: 700,
        fontSize: 16,
        marginBottom: 16,
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        color: colors.text,
        paddingBottom: 12,
        borderBottom: `2px solid ${colors.border}20`,
      }}>
        <span style={{ textTransform: "capitalize" }}>
          {status.replace("_", " ").toLowerCase()}
        </span>
        <span style={{
          background: colors.border,
          color: "#fff",
          borderRadius: 12,
          fontSize: 12,
          padding: "4px 10px",
          fontWeight: 600,
          minWidth: 24,
          textAlign: "center",
        }}>
          {count}
        </span>
      </div>
      <div style={{
        flex: 1,
        overflowY: "auto",
        paddingRight: 4,
        marginRight: -4,
      }}>
        {children}
      </div>
    </div>
  );
}

function TaskCardContent({ task, onEdit, onComplete, onEscalate }: TaskCardContentProps) {
  const formatDate = (dateString?: string) => {
    if (!dateString) return null;
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <>
      <div style={{ display: "flex", alignItems: "center", gap: 6, marginBottom: 8 }}>
        <span style={{
          background: PRIORITY_COLORS[task.priority] || "#e5e7eb",
          color: "#fff",
          borderRadius: 6,
          fontSize: 11,
          padding: "3px 8px",
          fontWeight: 600,
          textTransform: "uppercase",
          letterSpacing: "0.5px"
        }}>{task.priority}</span>
        <span style={{
          background: "#f3f4f6",
          color: "#374151",
          borderRadius: 6,
          fontSize: 11,
          padding: "3px 8px",
          fontWeight: 500,
          textTransform: "capitalize"
        }}>{task.type}</span>
      </div>

      <div style={{
        fontWeight: 600,
        fontSize: 15,
        marginBottom: 6,
        lineHeight: 1.3,
        color: "#1f2937"
      }}>
        {task.title}
      </div>

      {task.description && (
        <div style={{
          fontSize: 13,
          color: "#6b7280",
          marginBottom: 8,
          lineHeight: 1.4,
          display: "-webkit-box",
          WebkitLineClamp: 2,
          WebkitBoxOrient: "vertical",
          overflow: "hidden"
        }}>
          {task.description}
        </div>
      )}

      <div style={{
        fontSize: 11,
        color: "#9ca3af",
        marginBottom: 8,
        display: "flex",
        flexDirection: "column",
        gap: 2
      }}>
        {task.createdAt && (
          <div>Created: {formatDate(task.createdAt)}</div>
        )}
        {task.assignedAt && (
          <div>Assigned: {formatDate(task.assignedAt)}</div>
        )}
      </div>

      <div style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        marginTop: "auto",
        paddingTop: 8,
        borderTop: "1px solid #f3f4f6"
      }}>
        <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
          <span
            title="Edit task"
            style={{
              cursor: "pointer",
              padding: "4px",
              borderRadius: "4px",
              transition: "background 0.2s",
              fontSize: 14
            }}
            onClick={e => { e.stopPropagation(); onEdit(); }}
            onMouseEnter={e => (e.target as HTMLElement).style.background = "#f3f4f6"}
            onMouseLeave={e => (e.target as HTMLElement).style.background = "transparent"}
          >
            ✏️
          </span>
          {task.status !== "COMPLETED" && (
            <span
              title="Mark as complete"
              style={{
                cursor: "pointer",
                padding: "4px",
                borderRadius: "4px",
                transition: "background 0.2s",
                fontSize: 14
              }}
              onClick={e => { e.stopPropagation(); onComplete(); }}
              onMouseEnter={e => (e.target as HTMLElement).style.background = "#dcfce7"}
              onMouseLeave={e => (e.target as HTMLElement).style.background = "transparent"}
            >
              ✅
            </span>
          )}
          {task.status !== "ESCALATED" && task.status !== "COMPLETED" && (
            <span
              title="Escalate task"
              style={{
                cursor: "pointer",
                padding: "4px",
                borderRadius: "4px",
                transition: "background 0.2s",
                fontSize: 14
              }}
              onClick={e => { e.stopPropagation(); onEscalate(); }}
              onMouseEnter={e => (e.target as HTMLElement).style.background = "#fef3c7"}
              onMouseLeave={e => (e.target as HTMLElement).style.background = "transparent"}
            >
              ⚠️
            </span>
          )}
        </div>

        {task.assignedUser && (
          <div style={{
            fontSize: 10,
            color: "#6b7280",
            background: "#f9fafb",
            padding: "2px 6px",
            borderRadius: 4,
            maxWidth: 80,
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap"
          }}>
            {task.assignedUser.name}
          </div>
        )}
      </div>
    </>
  );
}

function AgentTaskDialog({ open, task, onClose, onSave }: AgentTaskDialogProps) {
  const [editTask, setEditTask] = React.useState(task);
  const [isLoading, setIsLoading] = React.useState(false);

  React.useEffect(() => {
    setEditTask(task);
  }, [task]);

  if (!open || !task) return null;

  const formatDate = (dateString?: string) => {
    if (!dateString) return "Not set";
    return new Date(dateString).toLocaleString();
  };

  const handleSave = async () => {
    if (!editTask) return;
    setIsLoading(true);
    try {
      await onSave(editTask);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={v => { if (!v) onClose(); }}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">Task Details</DialogTitle>
          <DialogDescription>View and update your task information</DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Task Status and Priority Info */}
          <div className="grid grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
            <div>
              <label className="block text-xs font-medium text-gray-500 mb-1">Priority</label>
              <span
                className="inline-block px-3 py-1 rounded-full text-white text-sm font-medium"
                style={{ background: PRIORITY_COLORS[task.priority] || '#888' }}
              >
                {task.priority}
              </span>
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-500 mb-1">Type</label>
              <span className="inline-block px-3 py-1 rounded-full bg-gray-200 text-gray-700 text-sm font-medium">
                {task.type}
              </span>
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-500 mb-1">Status</label>
              <span className="inline-block px-3 py-1 rounded-full bg-blue-100 text-blue-800 text-sm font-medium">
                {task.status.replace('_', ' ')}
              </span>
            </div>
          </div>

          {/* Editable Fields */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Title</label>
              <input
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={editTask?.title || ""}
                onChange={e => editTask && setEditTask({ ...editTask, title: e.target.value })}
                placeholder="Enter task title"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
              <textarea
                className="w-full border border-gray-300 rounded-lg px-3 py-2 min-h-[100px] focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={editTask?.description || ""}
                onChange={e => editTask && setEditTask({ ...editTask, description: e.target.value })}
                placeholder="Enter task description"
              />
            </div>
          </div>

          {/* Task Metadata */}
          <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
            <div>
              <label className="block text-xs font-medium text-gray-500 mb-1">Created</label>
              <p className="text-sm text-gray-700">{formatDate(task.createdAt)}</p>
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-500 mb-1">Assigned</label>
              <p className="text-sm text-gray-700">{formatDate(task.assignedAt)}</p>
            </div>
            {task.assignedUser && (
              <div className="col-span-2">
                <label className="block text-xs font-medium text-gray-500 mb-1">Assigned To</label>
                <p className="text-sm text-gray-700">{task.assignedUser.name} ({task.assignedUser.email})</p>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <button
              type="button"
              className="px-4 py-2 rounded-lg bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="button"
              className="px-4 py-2 rounded-lg bg-blue-600 text-white hover:bg-blue-700 transition-colors disabled:opacity-50"
              onClick={handleSave}
              disabled={isLoading}
            >
              {isLoading ? "Saving..." : "Save Changes"}
            </button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export function AgentKanbanBoard({ tasks, onStatusChange }: AgentKanbanBoardProps) {
  const [modalTask, setModalTask] = useState<Task | null>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [activeTask, setActiveTask] = useState<Task | null>(null);

  // Configure sensors for accessibility
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor)
  );

  // Group tasks by status
  const columns = TASK_STATUSES.map((status) => ({
    status,
    tasks: tasks.filter((t) => t.status === status),
  }));

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const task = tasks.find((t) => t.id === String(active.id));
    setActiveTask(task || null);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    setActiveTask(null); // Clear the active task

    if (!over || !active) return;
    const taskId = String(active.id);
    const newStatus = String(over.id);
    const task = tasks.find((t) => t.id === taskId);
    if (task && task.status !== newStatus && TASK_STATUSES.includes(newStatus)) {
      onStatusChange(taskId, newStatus);
    }
  };

  const handleEdit = (task: Task) => {
    setModalTask(task);
    setModalOpen(true);
  };

  const handleModalSave = () => {
    setModalOpen(false);
    setModalTask(null);
    // TODO: Add callback to update task details
  };

  return (
    <div style={{ position: "relative", zIndex: 0 }}>
      <DndContext
        sensors={sensors}
        collisionDetection={rectIntersection}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <div style={{
          display: "flex",
          gap: 20,
          overflowX: "auto",
          paddingBottom: 8,
          minHeight: 500,
          position: "relative",
          zIndex: 1
        }}>
          {columns.map((col) => (
            <DroppableColumn key={col.status} status={col.status} count={col.tasks.length}>
              <SortableContext items={col.tasks.map((t) => t.id)} strategy={verticalListSortingStrategy}>
                {col.tasks.map((task) => (
                  <DraggableTask key={task.id} task={task} onClick={() => handleEdit(task)}>
                    <TaskCardContent
                      task={task}
                      onEdit={() => handleEdit(task)}
                      onComplete={() => onStatusChange(task.id, "COMPLETED")}
                      onEscalate={() => onStatusChange(task.id, "ESCALATED")}
                    />
                  </DraggableTask>
                ))}
                {col.tasks.length === 0 && (
                  <div style={{
                    padding: 20,
                    textAlign: "center",
                    color: "#9ca3af",
                    fontSize: 14,
                    fontStyle: "italic"
                  }}>
                    No tasks
                  </div>
                )}
              </SortableContext>
            </DroppableColumn>
          ))}
        </div>
        <DragOverlay>
          {activeTask ? (
            <div style={{
              background: "#fff",
              padding: 16,
              borderRadius: 12,
              boxShadow: "0 20px 60px rgba(0,0,0,0.3)",
              border: "2px solid #3b82f6",
              minHeight: 120,
              display: "flex",
              flexDirection: "column",
              opacity: 1,
              cursor: "grabbing",
              width: 280,
            }}>
              <div style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "flex-start",
                marginBottom: 8,
              }}>
                <div style={{
                  display: "flex",
                  alignItems: "center",
                  gap: 8,
                }}>
                  <div style={{
                    padding: "4px 8px",
                    borderRadius: 6,
                    fontSize: 12,
                    fontWeight: 600,
                    background: PRIORITY_COLORS[activeTask.priority as keyof typeof PRIORITY_COLORS] || "#9ca3af",
                    color: "#fff",
                  }}>
                    {activeTask.priority}
                  </div>
                  <div style={{
                    padding: "4px 8px",
                    borderRadius: 6,
                    fontSize: 12,
                    fontWeight: 600,
                    background: "#f3f4f6",
                    color: "#374151",
                  }}>
                    {activeTask.type}
                  </div>
                </div>
              </div>
              <h3 style={{
                fontSize: 16,
                fontWeight: 600,
                color: "#1f2937",
                marginBottom: 8,
                lineHeight: 1.4,
              }}>
                {activeTask.title}
              </h3>
              <p style={{
                fontSize: 14,
                color: "#6b7280",
                lineHeight: 1.5,
                marginBottom: 12,
                display: "-webkit-box",
                WebkitLineClamp: 2,
                WebkitBoxOrient: "vertical",
                overflow: "hidden",
              }}>
                {activeTask.description}
              </p>
            </div>
          ) : null}
        </DragOverlay>
      </DndContext>
      <AgentTaskDialog
        open={modalOpen}
        task={modalTask}
        onClose={() => setModalOpen(false)}
        onSave={handleModalSave}
      />
    </div>
  );
} 